package com.concise.modular.laweducation.service.impl;

import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.consts.MediaTypeConstant;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.file.util.OssBootUtil;
import com.concise.common.file.util.OssSignedUrlUtil;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.util.HttpServletUtil;
import com.concise.common.util.PoiUtil;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.LoginEmpInfo;
import com.concise.core.login.SysLoginUser;
import com.concise.modular.home.vo.HomeResourceVo;
import com.concise.modular.laweducation.entity.LawEducationResourcesView;
import com.concise.modular.laweducation.entity.LeColumn;
import com.concise.modular.laweducation.entity.LeColumnResources;
import com.concise.modular.laweducation.entity.LeResources;
import com.concise.modular.laweducation.entity.LerAudit;
import com.concise.modular.laweducation.entity.LerLabel;
import com.concise.modular.laweducation.enums.LabelMappingEnum;
import com.concise.modular.laweducation.mapper.LeResourcesMapper;
import com.concise.modular.laweducation.param.LeResourcesParam;
import com.concise.modular.laweducation.service.LawEducationResourcesViewService;
import com.concise.modular.laweducation.service.LeColumnResourcesService;
import com.concise.modular.laweducation.service.LeColumnService;
import com.concise.modular.laweducation.service.LeResourcesService;
import com.concise.modular.laweducation.service.LerAuditService;
import com.concise.modular.laweducation.service.LerLabelService;
import com.concise.modular.utils.FileUploaderUtils;
import com.concise.modular.utils.RichTextToWordExporterUtils;
import com.concise.sys.modular.dict.entity.SysDictData;
import com.concise.sys.modular.dict.entity.SysDictType;
import com.concise.sys.modular.dict.service.SysDictDataService;
import com.concise.sys.modular.dict.service.SysDictTypeService;
import com.concise.sys.modular.org.entity.OrgLevelVo;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;

/**
 * 普法资源(信息)service接口实现类
 *
 * <AUTHOR>
 * @date 2024-03-25 14:38:22
 */
@Service
public class LeResourcesServiceImpl extends ServiceImpl<LeResourcesMapper, LeResources> implements LeResourcesService {

    @Resource
    private LerLabelService lerLabelService;
    @Resource
    private LerAuditService leAuditService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private SysOrgService sysOrgService;

    @Resource
    private LeColumnService leColumnService;

    @Resource
    private LeColumnResourcesService leColumnResourcesService;
    @Resource
    private LawEducationResourcesViewService lawEducationResourcesViewService;
    @Resource
    private SysDictDataService sysDictDataService;
    @Resource
    private SysDictTypeService sysDictTypeService;

    @Override
    public PageResult<LeResources> page(LeResourcesParam param) {
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        param.setLevel(sysLoginUser.getLevel().toString());

        if (!LoginContextHolder.me().isSuperAdmin()) {
            if ("3".equals(param.getLevel())) {
                // 普法信息共享（区县权限）
                queryWrapper.lambda().in(LeResources::getDeptId, sysLoginUser.getDataScopes())
                        .ne(LeResources::getStatus, 5)
                        .ne(LeResources::getStatus, 51);
            } else if ("2".equals(param.getLevel())) {
                // 普法信息审核-待（市级权限）
                // queryWrapper.lambda().like(LeResources::getDeptIds, orgId);
                queryWrapper.lambda().in(LeResources::getDeptId, sysLoginUser.getDataScopes());
            } else if ("1".equals(param.getLevel())) {
                if (ObjectUtil.isNotEmpty(param.getStatus())) {
                    if (param.getStatus() == 0) {
                        queryWrapper.lambda().eq(LeResources::getStatus, param.getStatus()).ne(LeResources::getLevel, 3)
                                .ne(LeResources::getLevel, 2);
                    }
                } else {
                    // 普法信息库（省/市权限）
                    queryWrapper.lambda().and(e -> e.in(LeResources::getStatus, Arrays.asList(0, 1, 5, 51, 6))
                            .or(i -> i.eq(LeResources::getStatus, 0).ne(LeResources::getLevel, 3)
                                    .ne(LeResources::getLevel, 2)));
                }

            }
        }

        if (ObjectUtil.isNotNull(param)) {

            // 根据资源类别 查询
            if (ObjectUtil.isNotEmpty(param.getType())) {
                queryWrapper.lambda().likeRight(LeResources::getType, param.getType());
            }
            if (ObjectUtil.isNotEmpty(param.getTypeList())) {
                queryWrapper.lambda().in(LeResources::getType, param.getTypeList());
            }

            if (ObjectUtil.isNotEmpty(param.getSource())) {
                queryWrapper.lambda().eq(LeResources::getSource, param.getSource());
            }
            // 根据标题 查询
            if (ObjectUtil.isNotEmpty(param.getTitle())) {
                queryWrapper.lambda().like(LeResources::getTitle, param.getTitle());
            }
            // 根据单位id 查询
            if (ObjectUtil.isNotEmpty(param.getDeptId())) {
                Set<String> deptIds = sysOrgService.getDeptIds(param.getDeptId());
                queryWrapper.lambda().in(LeResources::getDeptId, deptIds);
            }

            if (ObjectUtil.isNotEmpty(param.getLabelId())) {
                queryWrapper.lambda().like(LeResources::getLabelIds, param.getLabelId());
            }
            if (ObjectUtil.isNotEmpty(param.getStatus())) {
                queryWrapper.lambda().eq(LeResources::getStatus, param.getStatus());
            } else if (ObjectUtil.isNotEmpty(param.getSearchStatus()) && 1 == param.getSearchStatus()) {
                queryWrapper.lambda().and(i -> i.eq(LeResources::getStatus, 1)
                        .or().eq(LeResources::getStatus, 3));
            } else if (ObjectUtil.isNotEmpty(param.getSearchStatus()) && 2 == param.getSearchStatus()) {
                // queryWrapper.lambda().ne(LeResources::getStatus,
                // 9).ne(LeResources::getStatus, 3).ne(LeResources::getStatus, 51);
                queryWrapper.lambda()
                        .and(item -> item
                                .or(e -> e.in(LeResources::getStatus, 1, 2, 5, 6).isNull(LeResources::getMergeId))
                                .or(e -> e.eq(LeResources::getStatus, 51).eq(LeResources::getCreateUserId,
                                        sysLoginUser.getId())));
            } else {
                queryWrapper.lambda().ne(LeResources::getStatus, 9);
            }
            // 是否配置栏目
            if (ObjectUtil.isNotEmpty(param.getContactColumn())) {
                if ("1".equals(param.getContactColumn())) {
                    queryWrapper.lambda().isNotNull(LeResources::getColumnId);
                } else {
                    queryWrapper.lambda().and(i -> i.eq(LeResources::getStatus, 1)
                            .or().eq(LeResources::getStatus, 5));
                }
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper
                        .apply("date_format (create_time,'%Y-%m-%d') >= date_format('" + param.getSearchBeginTime()
                                + "','%Y-%m-%d')")
                        .apply("date_format (create_time,'%Y-%m-%d') <= date_format('" + param.getSearchEndTime()
                                + "','%Y-%m-%d')");
            }
        }
        queryWrapper.lambda().orderByDesc(LeResources::getCreateTime);
        Page<LeResources> page = PageFactory.defaultPage();
        page.setCurrent(param.getPageNo());
        page.setSize(param.getPageSize());
        PageResult<LeResources> pageResult = new PageResult<>(this.page(page, queryWrapper));
        List<LeResources> rows = pageResult.getRows();
        if (param.getNeedFile() == 0 && CollectionUtil.isNotEmpty(rows)) {
            List<String> likeIds = getLikeIds(LoginContextHolder.me().getSysLoginUserId());
            List<String> starIds = getStarIds(LoginContextHolder.me().getSysLoginUserId());
            for (LeResources row : rows) {
                if (ObjectUtil.isNotEmpty(row.getCover())) {
                    row.setCoverInfo(sysFileInfoService.getById(row.getCover()));
                } else {
                    List<SysFileInfo> list = sysFileInfoService
                            .list(new QueryWrapper<SysFileInfo>().lambda().eq(SysFileInfo::getReferId, row.getId()));
                    if (CollectionUtil.isNotEmpty(list)) {
                        for (SysFileInfo sysFileInfo : list) {
                            if (MediaTypeConstant.IMG_JPG.equals(sysFileInfo.getFileSuffix())
                                    || MediaTypeConstant.IMG_PNG.equals(sysFileInfo.getFileSuffix())) {
                                row.setCoverInfo(sysFileInfo);
                                break;
                            } else if (MediaTypeConstant.FILE_MP4.equals(sysFileInfo.getFileSuffix())) {
                                if (ObjectUtil.isNotEmpty(sysFileInfo.getScreenshotUrl())) {
                                    SysFileInfo sysFileInfo1 = new SysFileInfo();
                                    sysFileInfo1.setOutPath(sysFileInfo.getScreenshotUrl());
                                    sysFileInfo1.setFileSuffix(MediaTypeConstant.IMG_JPG);
                                    row.setCoverInfo(sysFileInfo1);
                                }
                            }
                        }
                    }
                }
                if (CollectionUtil.isNotEmpty(likeIds) && likeIds.contains(row.getId())) {
                    row.setIsLike(Boolean.TRUE);
                }
                if (CollectionUtil.isNotEmpty(starIds) && starIds.contains(row.getId())) {
                    row.setIsStar(Boolean.TRUE);
                }
            }
        }
        return pageResult;
    }

    @Override
    public List<LeResources> list(LeResourcesParam leResourcesParam) {
        return this.list();
    }

    /**
     * 设置部门ID和名称（司法局）
     * 优化逻辑：
     * 1. 如果有上级组织ID且不等于当前组织ID，直接使用上级组织信息
     * 2. 否则查找符合条件的上级组织（treeType=1的组织）
     * 3. 如果找不到符合条件的上级组织，使用当前组织信息
     *
     * @param loginEmpInfo 登录员工信息
     * @param model        资源模型
     */
    private void setDeptIdAndNameSfj(LoginEmpInfo loginEmpInfo, LeResources model) {
        // 情况1: 如果有上级组织ID且不等于当前组织ID，直接使用上级组织信息
        if (ObjectUtil.isNotEmpty(loginEmpInfo.getPOrgId()) && !loginEmpInfo.getPOrgId().equals(loginEmpInfo.getOrgId())) {
            model.setDeptId(loginEmpInfo.getPOrgId());
            model.setDeptName(loginEmpInfo.getPOrgName());
            return;
        }

        // 情况2: 尝试查找符合条件的上级组织
        SysOrg currentOrg = sysOrgService.getById(loginEmpInfo.getOrgId());
        if (ObjectUtil.isEmpty(currentOrg) || ObjectUtil.isEmpty(currentOrg.getPid())) {
            // 找不到上级组织，使用当前组织信息
            model.setDeptId(loginEmpInfo.getOrgId());
            model.setDeptName(loginEmpInfo.getOrgName());
            return;
        }

        // 查找符合条件的上级组织（treeType=1的组织）
        SysOrg targetOrg = findTargetParentOrg(currentOrg);

        // 情况3: 如果找到了符合条件的上级组织，使用该组织信息
        if (targetOrg != null) {
            model.setDeptId(targetOrg.getId());
            model.setDeptName(targetOrg.getOrgName());
        } else {
            // 找不到符合条件的上级组织，使用当前组织信息
            model.setDeptId(loginEmpInfo.getOrgId());
            model.setDeptName(loginEmpInfo.getOrgName());
        }
    }

    /**
     * 查找符合条件的上级组织（treeType=1的组织）
     * 最多向上查找3层
     *
     * @param org 当前组织
     * @return 符合条件的上级组织，如果找不到则返回null
     */
    private SysOrg findTargetParentOrg(SysOrg org) {
        if (org == null || ObjectUtil.isEmpty(org.getPid())) {
            return null;
        }

        // 向上最多查找3层
        // 第一层父组织
        SysOrg parentOrg = sysOrgService.getById(org.getPid());
        if (parentOrg == null) {
            return null;
        }

        // 检查第一层父组织是否符合条件
        if (ObjectUtil.isNotEmpty(parentOrg.getTreeType()) && parentOrg.getTreeType() == 1) {
            return parentOrg;
        }

        // 第二层父组织
        if (ObjectUtil.isEmpty(parentOrg.getPid())) {
            return null;
        }
        SysOrg grandParentOrg = sysOrgService.getById(parentOrg.getPid());
        if (grandParentOrg == null) {
            return null;
        }

        // 检查第二层父组织是否符合条件
        if (ObjectUtil.isNotEmpty(grandParentOrg.getTreeType()) && grandParentOrg.getTreeType() == 1) {
            return grandParentOrg;
        }

        // 第三层父组织
        if (ObjectUtil.isEmpty(grandParentOrg.getPid())) {
            return null;
        }
        SysOrg greatGrandParentOrg = sysOrgService.getById(grandParentOrg.getPid());
        if (greatGrandParentOrg == null) {
            return null;
        }

        // 检查第三层父组织是否符合条件
        if (ObjectUtil.isNotEmpty(greatGrandParentOrg.getTreeType()) && greatGrandParentOrg.getTreeType() == 1) {
            return greatGrandParentOrg;
        }

        // 找不到符合条件的上级组织
        return null;
    }

    @Override
    public void add(LeResourcesParam param) {
        LeResources model = new LeResources();
        BeanUtil.copyProperties(param, model);
        SysLoginUser user = LoginContextHolder.me().getSysLoginUser();
        LoginEmpInfo loginEmpInfo = user.getLoginEmpInfo();
        OrgLevelVo deptIdsApplySft = sysOrgService.getLevelAndOrgPermission(loginEmpInfo.getOrgId());
        model.setLevel(deptIdsApplySft.getLevel());
        model.setCreateUser(user.getName());
        model.setCreateTime(DateUtil.date());
        model.setDataSource(param.getDataSource());

        setDeptIdAndNameSfj(loginEmpInfo, model);
        model.setSource("0".equals(model.getType()) ? "0" : "1");
        // 查询本级及下级数据时使用的冗余
        model.setDeptIds(sysOrgService.getPids(model.getDeptId()));
        String id = IdUtil.fastSimpleUUID();
        model.setId(id);
        model.setCreateUserId(user.getId());
        this.save(model);
        param.setId(id);
        lerLabelService.updateLabelContact(id, param.getLabelList());
        for (String fileId : param.getFileList()) {
            sysFileInfoService.lambdaUpdate().set(SysFileInfo::getReferId, id).eq(SysFileInfo::getId, fileId).update();
        }
        if (CollectionUtil.isNotEmpty(param.getVideoList())) {
            for (String fileId : param.getVideoList()) {
                sysFileInfoService.lambdaUpdate().set(SysFileInfo::getReferId, id).set(SysFileInfo::getReferType, "video").eq(SysFileInfo::getId, fileId).update();
            }
        }

        if (0 == param.getStatus()) {
            leAuditService.save(model);
        }
    }

    @Override
    public void addResource(LeResourcesParam param) {
        LeResources model = new LeResources();
        BeanUtil.copyProperties(param, model);

        SysLoginUser user = LoginContextHolder.me().getSysLoginUser();
        model.setCreateUser(user.getName());
        model.setCreateTime(DateUtil.date());
        setDeptIdAndNameSfj(user.getLoginEmpInfo(), model);
        model.setSource("0".equals(model.getType()) ? "0" : "1");
        // 查询本级及下级数据时使用的冗余
        model.setDeptIds(sysOrgService.getPids(model.getDeptId()));
        String id = IdUtil.fastSimpleUUID();
        model.setId(id);
        this.save(model);
        param.setId(id);
        lerLabelService.updateLabelContact(id, param.getLabelList());
        for (String fileId : param.getFileList()) {
            sysFileInfoService.lambdaUpdate().set(SysFileInfo::getReferId, id).eq(SysFileInfo::getId, fileId).update();
        }

        if (0 == param.getStatus()) {
            leAuditService.saveLaw(model);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String id, String reason) {
        this.lambdaUpdate()
                .set(LeResources::getStatus, 9)
                .set(LeResources::getDelReason, reason)
                .eq(LeResources::getId, id)
                .update();
    }

    @Override
    public void deleteColumnContact(String columnId) {
        List<String> resIdList = this.lambdaQuery().eq(LeResources::getColumnId, columnId).list().stream()
                .map(LeResources::getId).collect(Collectors.toList());
        for (String id : resIdList) {
            escape(id);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(LeResourcesParam param) {
        LeResources model = this.getById(param.getId());
        BeanUtil.copyProperties(param, model);
        SysLoginUser user = LoginContextHolder.me().getSysLoginUser();
        model.setCreateUser(user.getName());
        model.setCreateTime(DateUtil.date());
        setDeptIdAndNameSfj(user.getLoginEmpInfo(), model);
        model.setSource("0".equals(model.getType()) ? "0" : "1");
        this.updateById(model);
        lerLabelService.updateLabelContact(param.getId(), param.getLabelList());
        for (String fileId : param.getFileList()) {
            sysFileInfoService.lambdaUpdate().set(SysFileInfo::getReferId, param.getId()).eq(SysFileInfo::getId, fileId)
                    .update();
        }

        if (0 == param.getStatus()) {
            leAuditService.save(model);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editResource(LeResourcesParam param) {
        LeResources model = this.getById(param.getId());
        BeanUtil.copyProperties(param, model);
        SysLoginUser user = LoginContextHolder.me().getSysLoginUser();
        model.setCreateUser(user.getName());
        model.setCreateTime(DateUtil.date());
        setDeptIdAndNameSfj(user.getLoginEmpInfo(), model);
        model.setSource("0".equals(model.getType()) ? "0" : "1");
        this.updateById(model);
        lerLabelService.updateLabelContact(param.getId(), param.getLabelList());
        for (String fileId : param.getFileList()) {
            sysFileInfoService.lambdaUpdate().set(SysFileInfo::getReferId, param.getId()).eq(SysFileInfo::getId, fileId)
                    .update();
        }

        if (0 == param.getStatus()) {
            leAuditService.saveLaw(model);
        }
    }

    @Override
    public void viewById(String id, Integer type) {
        LeResources model = this.getById(id);
        LawEducationResourcesView lawEducationResourcesView = lawEducationResourcesViewService.getOne(
                new QueryWrapper<LawEducationResourcesView>().lambda().eq(LawEducationResourcesView::getResourceId, id)
                        .eq(LawEducationResourcesView::getUserId, LoginContextHolder.me().getSysLoginUser().getId()));
        if (ObjectUtil.isEmpty(lawEducationResourcesView)) {
            lawEducationResourcesView = new LawEducationResourcesView();
            lawEducationResourcesView.setResourceId(id);
            lawEducationResourcesView.setUserId(LoginContextHolder.me().getSysLoginUser().getId());
            lawEducationResourcesView.setUserName(LoginContextHolder.me().getSysLoginUser().getName());
            lawEducationResourcesView.setLikes(false);
            lawEducationResourcesView.setStar(false);
            lawEducationResourcesView.setTransmit(false);
            lawEducationResourcesView.setCreateTime(DateUtil.date());
            lawEducationResourcesViewService.save(lawEducationResourcesView);
        }
        // 1 浏览 2 点赞 3 收藏 4 转发
        if (1 == type) {
            if (ObjectUtil.isEmpty(model.getViewNum())) {
                model.setViewNum(0);
            }
            model.setViewNum(model.getViewNum() + 1);
        } else if (2 == type) {
            if (ObjectUtil.isEmpty(model.getLikesNum())) {
                model.setLikesNum(0);
            }
            if (lawEducationResourcesView.isLikes()) {
                model.setLikesNum(model.getLikesNum() - 1);
                lawEducationResourcesView.setLikes(false);
            } else {
                model.setLikesNum(model.getLikesNum() + 1);
                lawEducationResourcesView.setLikes(true);
            }
        } else if (3 == type) {
            if (ObjectUtil.isEmpty(model.getStarNum())) {
                model.setStarNum(0);
            }
            if (lawEducationResourcesView.isStar()) {
                model.setStarNum(model.getStarNum() - 1);
                lawEducationResourcesView.setStar(false);
            } else {
                model.setStarNum(model.getStarNum() + 1);
                lawEducationResourcesView.setStar(true);
            }
        } else if (4 == type) {
            if (lawEducationResourcesView.isTransmit()) {
                model.setTransmitNum(model.getTransmitNum() - 1);
                lawEducationResourcesView.setTransmit(false);
            } else {
                model.setTransmitNum(model.getTransmitNum() + 1);
                lawEducationResourcesView.setTransmit(true);
            }
        }
        this.updateById(model);
        lawEducationResourcesView.setUpdateTime(DateUtil.date());
        lawEducationResourcesViewService.updateById(lawEducationResourcesView);
    }

    @Override
    public void audit(LeResourcesParam param) {

        SysLoginUser user = LoginContextHolder.me().getSysLoginUser();
        LeResources model = this.getById(param.getId());
        param.setCreateUser(null);
        BeanUtil.copyProperties(param, model);
        if (ObjectUtil.isNotEmpty(param.getLabelList())) {
            lerLabelService.updateLabelContact(param.getId(), param.getLabelList());
        }
        if (ObjectUtil.isNotEmpty(param.getFileList())) {
            for (String fileId : param.getFileList()) {
                sysFileInfoService.lambdaUpdate().set(SysFileInfo::getReferId, param.getId())
                        .eq(SysFileInfo::getId, fileId).update();
            }
        }
        LoginEmpInfo loginEmpInfo = user.getLoginEmpInfo();
        String deptName = loginEmpInfo.getPOrgName() != null
                ? (loginEmpInfo.getPOrgName() + SymbolConstant.LEFT_DIVIDE + loginEmpInfo.getOrgName())
                : loginEmpInfo.getOrgName();
        leAuditService.audit(param.getId(), param.getAuditResult(), param.getAuditOption(), user.getName(), deptName,
                model.getSource());
        model.setStatus("1".equals(param.getAuditResult()) ? 1 : 3);
        this.updateById(model);
    }

    @Override
    public void merge(LeResourcesParam param) {
        SysLoginUser user = LoginContextHolder.me().getSysLoginUser();
        String mergeId = IdUtil.fastSimpleUUID();
        for (String id : param.getIdList()) {
            LoginEmpInfo loginEmpInfo = user.getLoginEmpInfo();
            String deptName = loginEmpInfo.getPOrgName() != null
                    ? (loginEmpInfo.getPOrgName() + SymbolConstant.LEFT_DIVIDE + loginEmpInfo.getOrgName())
                    : loginEmpInfo.getOrgName();
            leAuditService.audit(id, param.getAuditResult(), param.getAuditOption(), user.getName(), deptName, "0");
        }
        this.lambdaUpdate()
                .set(LeResources::getStatus, 2)
                .set(LeResources::getMergeId, mergeId)
                .in(LeResources::getId, param.getIdList()).update();

        // 保存合并后的信息
        LeResources model = new LeResources();
        BeanUtil.copyProperties(param, model);
        // 查询本级及下级数据时使用的冗余
        model.setDeptIds(sysOrgService.getPids(model.getDeptId()));
        model.setId(mergeId);
        model.setStatus(1);
        model.setSource("0");
        this.save(model);
        LoginEmpInfo loginEmpInfo = user.getLoginEmpInfo();
        String deptName = loginEmpInfo.getPOrgName() != null
                ? (loginEmpInfo.getPOrgName() + SymbolConstant.LEFT_DIVIDE + loginEmpInfo.getOrgName())
                : loginEmpInfo.getOrgName();
        leAuditService.merge(mergeId, param.getAuditResult(), param.getAuditOption(), user.getName(), deptName);
        lerLabelService.updateLabelContact(mergeId, param.getLabelList());
        for (String fileId : param.getFileList()) {
            //避免原普法信息附件丢失，复制一份
            SysFileInfo sysFileInfo = sysFileInfoService.getById(fileId);
            sysFileInfo.setId(IdWorker.getId());
            sysFileInfo.setReferId(mergeId);
            sysFileInfoService.save(sysFileInfo);
//            sysFileInfoService.lambdaUpdate().set(SysFileInfo::getReferId, mergeId).eq(SysFileInfo::getId, fileId)
//                    .update();
        }

    }

    @Override
    public void contact(String id, String columnId, String columnName) {
        leAuditService.contact(id);
        this.lambdaUpdate()
                .set(LeResources::getStatus, 6)
                .set(LeResources::getColumnId, columnId)
                .set(LeResources::getColumnName, columnName)
                .eq(LeResources::getId, id).update();
    }

    @Override
    public void escape(String id) {
        this.lambdaUpdate()
                .set(LeResources::getStatus, 1)
                .set(LeResources::getColumnId, null)
                .set(LeResources::getColumnName, null)
                .eq(LeResources::getId, id).update();
        leAuditService.escape(id);
    }

    @Override
    public void withdraw(String id) {
        this.lambdaUpdate().set(LeResources::getStatus, -2).eq(LeResources::getId, id).update();
        leAuditService.lambdaUpdate().eq(LerAudit::getResourcesId, id).remove();
    }

    @Override
    public LeResources detail(LeResourcesParam leResourcesParam) {
        LeResources model = this.getById(leResourcesParam.getId());
        model.setLabelList(lerLabelService.selectLabelContact(model.getId()));
        model.setFileInfoList(sysFileInfoService.lambdaQuery().eq(SysFileInfo::getReferId, model.getId()).isNull(SysFileInfo::getReferType).list());
        model.setCoverInfo(sysFileInfoService.getById(model.getCover()));
        model.setVideoInfoList(sysFileInfoService.lambdaQuery().eq(SysFileInfo::getReferId, model.getId()).eq(SysFileInfo::getReferType, "video").list());
        model.setSteps(leAuditService.listStep(model.getId()));
        List<String> likeIds = getLikeIds(LoginContextHolder.me().getSysLoginUserId());
        List<String> starIds = getStarIds(LoginContextHolder.me().getSysLoginUserId());
        if (CollectionUtil.isNotEmpty(likeIds) && likeIds.contains(model.getId())) {
            model.setIsLike(Boolean.TRUE);
        }
        if (CollectionUtil.isNotEmpty(starIds) && starIds.contains(model.getId())) {
            model.setIsStar(Boolean.TRUE);
        }
        return model;
    }

    private QueryWrapper<LeResources> exportQuery(LeResourcesParam param) {
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(param.getId())) {
            queryWrapper.lambda().in(LeResources::getId, Arrays.asList(param.getId().split(",")));
        }
        // 根据资源类别 查询
        if (ObjectUtil.isNotEmpty(param.getType())) {
            queryWrapper.lambda().eq(LeResources::getType, param.getType());
        }
        // 根据标题 查询
        if (ObjectUtil.isNotEmpty(param.getTitle())) {
            queryWrapper.lambda().like(LeResources::getTitle, param.getTitle());
        }
        // 根据单位id 查询
        if (ObjectUtil.isNotEmpty(param.getDeptId())) {
            Set<String> deptIds = sysOrgService.getDeptIds(param.getDeptId());
            queryWrapper.lambda().in(LeResources::getDeptId, deptIds);
        }
        // 是否配置栏目
        if (ObjectUtil.isNotEmpty(param.getContactColumn())) {
            if ("1".equals(param.getContactColumn())) {
                queryWrapper.lambda().isNotNull(LeResources::getColumnId);
            } else {
                queryWrapper.lambda().and(i -> i.eq(LeResources::getStatus, 1)
                        .or().eq(LeResources::getStatus, 5));
            }
        } else {
            queryWrapper.lambda().and(i -> i.eq(LeResources::getStatus, 1)
                    .or().eq(LeResources::getStatus, 5)
                    .or().eq(LeResources::getStatus, 6));
        }

        // 根据时间范围查询
        if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
            queryWrapper
                    .apply("date_format (create_time,'%Y-%m-%d') >= date_format('" + param.getSearchBeginTime()
                            + "','%Y-%m-%d')")
                    .apply("date_format (create_time,'%Y-%m-%d') <= date_format('" + param.getSearchEndTime()
                            + "','%Y-%m-%d')");
        }
        queryWrapper.lambda().orderByDesc(LeResources::getCreateTime);
        return queryWrapper;
    }

    @Override
    public void export(LeResourcesParam param) {
        List<LeResources> list = this.list(exportQuery(param));
        for (LeResources leResources : list) {
            List<String> labelIds = lerLabelService.selectLabelContactName(leResources.getId());
            if (CollectionUtil.isNotEmpty(labelIds)) {

                leResources.setLabel(String.join(",", labelIds));
            }
            leResources.setContext(richTextToPlainText(leResources.getContext()));
        }
        PoiUtil.exportExcelWithStream(
                "普法信息资源" + DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_FORMAT) + ".xls", LeResources.class,
                list);

    }

    public static String richTextToPlainText(String richText) {
        if (richText == null) {
            return null;
        }
        // 去除所有HTML标签
        String plainText = richText.replaceAll("<[^>]+>", "");
        // 去除HTML转义字符（例如 &amp; -> &）
        plainText = StringEscapeUtils.unescapeHtml4(plainText);
        return plainText.trim();
    }

    @Override
    public List<LeColumn> recommend(String pid) {
        List<LeColumn> list = leColumnService.list(new QueryWrapper<LeColumn>().lambda().eq(LeColumn::getPid, pid)
                .eq(LeColumn::getStatus, 0).orderByAsc(LeColumn::getSort).orderByDesc(LeColumn::getCreateTime));
        List<String> likeIds = getLikeIds(LoginContextHolder.me().getSysLoginUserId());
        List<String> starIds = getStarIds(LoginContextHolder.me().getSysLoginUserId());
        List<LeColumn> matchList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (LeColumn leColumn : list) {
                // List<LeResources> resourcesList = this.list(new
                // QueryWrapper<LeResources>().lambda().like(LeResources::getColumnId,
                // leColumn.getId()).ne(LeResources::getStatus,
                // 9).orderByDesc(LeResources::getUpdateTime).last("limit 2"));
                List<LeResources> resourcesList = this.baseMapper.getRecommendTwo(leColumn.getId());
                if (CollectionUtil.isNotEmpty(resourcesList)) {
                    for (LeResources leResources : resourcesList) {
                        if (CollectionUtil.isNotEmpty(likeIds) && likeIds.contains(leResources.getId())) {
                            leResources.setIsLike(Boolean.TRUE);
                        }
                        if (CollectionUtil.isNotEmpty(starIds) && starIds.contains(leResources.getId())) {
                            leResources.setIsStar(Boolean.TRUE);
                        }
                        if (ObjectUtil.isNotEmpty(leResources.getCover())) {
                            leResources.setCoverInfo(sysFileInfoService.getById(leResources.getCover()));
                        } else {
                            List<SysFileInfo> fileInfoList = sysFileInfoService.lambdaQuery()
                                    .eq(SysFileInfo::getReferId, leResources.getId()).list();
                            if (CollectionUtil.isNotEmpty(fileInfoList)) {
                                for (SysFileInfo sysFileInfo : fileInfoList) {
                                    if (ObjectUtil.isNotEmpty(sysFileInfo.getScreenshotUrl())) {
                                        leResources.setCoverInfo(sysFileInfo);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    leColumn.setResourcesList(resourcesList);
                    matchList.add(leColumn);
                }
            }
        } else {
            LeColumn leColumn = new LeColumn();
            leColumn.setId("0");
            // List<LeResources> resourcesList = this.list(new
            // QueryWrapper<LeResources>().lambda().like(LeResources::getColumnId, pid));
            List<LeResources> resourcesList = this.baseMapper.getRecommendAll(pid);

            if (CollectionUtil.isNotEmpty(resourcesList)) {
                for (LeResources leResources : resourcesList) {
                    if (CollectionUtil.isNotEmpty(likeIds) && likeIds.contains(leResources.getId())) {
                        leResources.setIsLike(Boolean.TRUE);
                    }
                    if (CollectionUtil.isNotEmpty(starIds) && starIds.contains(leResources.getId())) {
                        leResources.setIsStar(Boolean.TRUE);
                    }
                    if (ObjectUtil.isNotEmpty(leResources.getCover())) {
                        leResources.setCoverInfo(sysFileInfoService.getById(leResources.getCover()));
                    } else {
                        List<SysFileInfo> fileInfoList = sysFileInfoService.lambdaQuery()
                                .eq(SysFileInfo::getReferId, leResources.getId()).list();
                        if (CollectionUtil.isNotEmpty(fileInfoList)) {
                            for (SysFileInfo sysFileInfo : fileInfoList) {
                                if (ObjectUtil.isNotEmpty(sysFileInfo.getScreenshotUrl())) {
                                    leResources.setCoverInfo(sysFileInfo);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            leColumn.setResourcesList(resourcesList);
            matchList.add(leColumn);
        }
        return matchList;
    }

    @Override
    public PageResult<LeResources> storePage(LeResourcesParam param) {
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        Integer level = sysLoginUser.getLevel();
        if (!LoginContextHolder.me().isSuperAdmin()) {
            queryWrapper.lambda().in(LeResources::getDeptId, sysLoginUser.getDataScopes());
        }
        // 省市还能看自己暂存的，不看合并的
        if (level == 1 || level == 2) {
            queryWrapper.lambda().and(item -> item
                    .or(e -> e.in(LeResources::getStatus, 1, 5, 6).isNull(LeResources::getMergeId))
                    .or(e -> e.eq(LeResources::getStatus, 51).eq(LeResources::getCreateUserId, sysLoginUser.getId())));
        }
        if (level == 3) {
            queryWrapper.lambda().in(LeResources::getStatus, 1, 2, 6);
        }

        if (ObjectUtil.isNotNull(param)) {

            if (ObjectUtil.isNotEmpty(param.getId())) {
                String[] split = param.getId().split(",");
                queryWrapper.lambda().in(LeResources::getId, Arrays.asList(split));
            }
            // 根据资源类别 查询
            if (ObjectUtil.isNotEmpty(param.getType())) {
                queryWrapper.lambda().likeRight(LeResources::getType, param.getType());
            }
            if (ObjectUtil.isNotEmpty(param.getTypeList())) {
                queryWrapper.lambda().in(LeResources::getType, param.getTypeList());
            }

            if (ObjectUtil.isNotEmpty(param.getEliminateIds())) {
                String[] split = param.getEliminateIds().split(",");
                queryWrapper.lambda().notIn(LeResources::getId, Arrays.asList(split));
            }
            if (ObjectUtil.isNotEmpty(param.getSource())) {
                queryWrapper.lambda().eq(LeResources::getSource, param.getSource());
            }
            // 根据标题 查询
            if (ObjectUtil.isNotEmpty(param.getTitle())) {
                queryWrapper.lambda().like(LeResources::getTitle, param.getTitle());
            }
            // 根据单位id 查询
            if (ObjectUtil.isNotEmpty(param.getDeptId())) {
                Set<String> deptIds = sysOrgService.getDeptIds(param.getDeptId());
                queryWrapper.lambda().in(LeResources::getDeptId, deptIds);
            }

            if (ObjectUtil.isNotEmpty(param.getLabelId())) {
                queryWrapper.lambda().like(LeResources::getLabelIds, param.getLabelId());
            }
            // 是否配置栏目
            if (ObjectUtil.isNotEmpty(param.getContactColumn())) {
                if ("1".equals(param.getContactColumn())) {
                    queryWrapper.lambda().isNotNull(LeResources::getColumnId);
                } else {
                    queryWrapper.lambda().and(i -> i.eq(LeResources::getStatus, 1)
                            .or().eq(LeResources::getStatus, 5)
                    );
//                    queryWrapper.lambda().isNotNull(LeResources::getColumnId);
                }
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper
                        .apply("date_format (create_time,'%Y-%m-%d') >= date_format('" + param.getSearchBeginTime()
                                + "','%Y-%m-%d')")
                        .apply("date_format (create_time,'%Y-%m-%d') <= date_format('" + param.getSearchEndTime()
                                + "','%Y-%m-%d')");
            }
        }
        queryWrapper.lambda().orderByDesc(LeResources::getCreateTime);
        Page<LeResources> page = PageFactory.defaultPage();
        page.setCurrent(param.getPageNo());
        page.setSize(param.getPageSize());
        PageResult<LeResources> pageResult = new PageResult<>(this.page(page, queryWrapper));
        List<LeResources> rows = pageResult.getRows();
        if (param.getNeedFile() == 0 && CollectionUtil.isNotEmpty(rows)) {
            List<String> likeIds = getLikeIds(LoginContextHolder.me().getSysLoginUserId());
            List<String> starIds = getStarIds(LoginContextHolder.me().getSysLoginUserId());
            for (LeResources row : rows) {
                if (ObjectUtil.isNotEmpty(row.getCover())) {
                    row.setCoverInfo(sysFileInfoService.getById(row.getCover()));
                }
                if (CollectionUtil.isNotEmpty(likeIds) && likeIds.contains(row.getId())) {
                    row.setIsLike(Boolean.TRUE);
                }
                if (CollectionUtil.isNotEmpty(starIds) && starIds.contains(row.getId())) {
                    row.setIsStar(Boolean.TRUE);
                }
            }
        }
        return pageResult;
    }

    @Override
    public List<String> getStarIds(String userId) {
        return this.baseMapper.getStarIds(userId);
    }

    @Override
    public List<String> getLikeIds(String userId) {
        return this.baseMapper.getLikeIds(userId);
    }

    @Override
    public PageResult<LeResources> mobilePage(LeResourcesParam param) {
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据资源类别 查询
            if (ObjectUtil.isNotEmpty(param.getType())) {
                queryWrapper.lambda().likeRight(LeResources::getType, param.getType());
            }
            if (ObjectUtil.isNotEmpty(param.getTypeList())) {
                queryWrapper.lambda().in(LeResources::getType, param.getTypeList());
            }

            if (ObjectUtil.isNotEmpty(param.getSource())) {
                queryWrapper.lambda().eq(LeResources::getSource, param.getSource());
            }
            if (ObjectUtil.isNotEmpty(param.getColumnId())) {
                queryWrapper.lambda().like(LeResources::getColumnId, param.getColumnId());
            }
            // 根据标题 查询
            if (ObjectUtil.isNotEmpty(param.getTitle())) {
                queryWrapper.lambda().like(LeResources::getTitle, param.getTitle());
            }
            // 根据单位id 查询
            if (ObjectUtil.isNotEmpty(param.getDeptId())) {
                queryWrapper.lambda().eq(LeResources::getDeptId, param.getDeptId());
            }
            if (ObjectUtil.isNotEmpty(param.getStatus())) {
                queryWrapper.lambda().eq(LeResources::getStatus, param.getStatus());
            } else if (ObjectUtil.isNotEmpty(param.getSearchStatus()) && 1 == param.getSearchStatus()) {
                queryWrapper.lambda().and(i -> i.eq(LeResources::getStatus, 1)
                        .or().eq(LeResources::getStatus, 3));
            } else if (ObjectUtil.isNotEmpty(param.getSearchStatus()) && 2 == param.getSearchStatus()) {
                queryWrapper.lambda().ne(LeResources::getStatus, 9).ne(LeResources::getStatus, 3)
                        .ne(LeResources::getStatus, 51);

            } else {
                queryWrapper.lambda().ne(LeResources::getStatus, 9);
            }
            // 是否配置栏目
            if (ObjectUtil.isNotEmpty(param.getContactColumn())) {
                if ("1".equals(param.getContactColumn())) {
                    queryWrapper.lambda().isNotNull(LeResources::getColumnId);
                } else {
                    queryWrapper.lambda().and(i -> i.eq(LeResources::getStatus, 1)
                            .or().eq(LeResources::getStatus, 5));
                }
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper
                        .apply("date_format (update_time,'%Y-%m-%d') >= date_format('" + param.getSearchBeginTime()
                                + "','%Y-%m-%d')")
                        .apply("date_format (update_time,'%Y-%m-%d') <= date_format('" + param.getSearchEndTime()
                                + "','%Y-%m-%d')");
            }
        }
        queryWrapper.lambda().isNotNull(LeResources::getColumnId).orderByDesc(LeResources::getCreateTime);
        PageResult<LeResources> pageResult = new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
        List<LeResources> rows = pageResult.getRows();
        if (CollectionUtil.isNotEmpty(rows)) {
            List<String> likeIds = getLikeIds(LoginContextHolder.me().getSysLoginUserId());
            List<String> starIds = getStarIds(LoginContextHolder.me().getSysLoginUserId());
            for (LeResources row : rows) {
                if (ObjectUtil.isNotEmpty(row.getCover())) {
                    row.setCoverInfo(sysFileInfoService.getById(row.getCover()));
                }
                if (CollectionUtil.isNotEmpty(likeIds) && likeIds.contains(row.getId())) {
                    row.setIsLike(Boolean.TRUE);
                }
                if (CollectionUtil.isNotEmpty(starIds) && starIds.contains(row.getId())) {
                    row.setIsStar(Boolean.TRUE);
                }
            }
        }
        return pageResult;
    }

    @Override
    public List<HomeResourceVo> cityResource(String name) {
        return this.baseMapper.cityResource(name);
    }

    @Override
    public List<HomeResourceVo> provinceResource() {
        return this.baseMapper.provinceResource();
    }

    @Override
    public List<HomeResourceVo> cityInformation(String name) {
        return this.baseMapper.cityInformation(name);

    }

    @Override
    public List<HomeResourceVo> provinceInformation() {
        return this.baseMapper.provinceInformation();

    }

    @Override
    public void exportPic(LeResourcesParam leResourcesParam) {
        HttpServletResponse response = HttpServletUtil.getResponse();
        List<LeResources> leResourcesList = this.list(exportQuery(leResourcesParam));
        List<SysFileInfo> sysFileInfoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(leResourcesList)) {
            for (LeResources leResources : leResourcesList) {
                List<SysFileInfo> list = sysFileInfoService.lambdaQuery()
                        .eq(SysFileInfo::getReferId, leResources.getId()).list();
                if (CollectionUtil.isNotEmpty(list) && list.size() > 1) {
                    for (int i = 0; i < list.size(); i++) {
                        SysFileInfo sysFileInfo = list.get(i);
                        sysFileInfo.setFileOriginName(leResources.getTitle() + SymbolConstant.LEFT_ROUND_BRACKETS
                                + (i + 1) + SymbolConstant.RIGHT_ROUND_BRACKETS + SymbolConstant.PERIOD
                                + sysFileInfo.getFileSuffix());
                    }
                    sysFileInfoList.addAll(list);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(sysFileInfoList)) {
            // 预处理文件名，确保唯一性
            Map<String, Integer> nameCountMap = new HashMap<>();
            for (SysFileInfo file : sysFileInfoList) {
                String baseName = file.getFileOriginName();
                String uniqueName = baseName;
                int count = nameCountMap.getOrDefault(baseName, 0);
                while (nameCountMap.containsKey(uniqueName)) {
                    count++;
                    int dotIndex = baseName.lastIndexOf('.');
                    if (dotIndex > 0) {
                        uniqueName = baseName.substring(0, dotIndex) + "(" + count + ")" + baseName.substring(dotIndex);
                    } else {
                        uniqueName = baseName + "(" + count + ")";
                    }
                }
                nameCountMap.put(uniqueName, 1);
                file.setFileOriginName(uniqueName);
            }

            // 设置响应头
            response.setContentType("application/zip");
            String zipName = "普法信息图片" + DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN) + ".zip";
            String encodedFilename;
            try {
                encodedFilename = URLEncoder.encode(zipName, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                encodedFilename = zipName;
            }
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFilename);

            // 单一try-with-resources只管理ZipOutputStream，避免嵌套关闭
            ZipOutputStream zipOut = null;
            try {
                zipOut = new ZipOutputStream(response.getOutputStream());

                // 逐个处理图片
                for (SysFileInfo sysFileInfo : sysFileInfoList) {
                    InputStream imageInputStream = null;
                    try {
                        // 获取图片流
                        imageInputStream = OssBootUtil.getInputStream(sysFileInfo.getFilePath());

                        // 写入ZIP
                        zipOut.putNextEntry(new ZipEntry(sysFileInfo.getFileOriginName()));
                        byte[] buffer = new byte[4096];
                        int len;
                        while ((len = imageInputStream.read(buffer)) != -1) {
                            zipOut.write(buffer, 0, len);
                        }
                        zipOut.closeEntry();
                    } catch (Exception e) {
                        // 只记录该图片错误，继续处理下一张
                        e.printStackTrace();
                    } finally {
                        // 确保imageInputStream关闭
                        if (imageInputStream != null) {
                            try {
                                imageInputStream.close();
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }

                // 正常完成所有图片处理后，主动完成ZIP
                zipOut.finish();
            } catch (Exception e) {
                // 主流程异常，只记录日志
                e.printStackTrace();
            } finally {
                // 确保zipOut始终被关闭
                if (zipOut != null) {
                    try {
                        zipOut.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        } else {
            // 没有图片时返回错误信息
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"code\":500,\"message\":\"没有可供导出的图片\"}");
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public void exportWord(LeResourcesParam leResourcesParam) {
        HttpServletResponse response = HttpServletUtil.getResponse();
        String orgName = "";
        List<LeResources> leResourcesList = this.list(exportQuery(leResourcesParam));
        LoginEmpInfo loginEmpInfo = LoginContextHolder.me().getSysLoginUser().getLoginEmpInfo();
        SysOrg sysOrg = sysOrgService.getById(loginEmpInfo.getOrgId());
        if (ObjectUtil.isNotEmpty(sysOrg)) {
            Integer level = sysOrg.getLevel();
            if (level == 1) {
                orgName = sysOrg.getName();
            } else {
                orgName = loginEmpInfo.getPOrgName() + loginEmpInfo.getOrgName();
            }
        }
        if (CollectionUtil.isNotEmpty(leResourcesList)) {
            List<String> list = new ArrayList<>();
            for (LeResources leResources : leResourcesList) {
                String context = leResources.getContext();
                if (ObjectUtil.isNotEmpty(leResources.getCreateTime())) {
                    if (context.contains("<p>")) {
                        // 在第一个p标签后插入日期
                        context = context.replaceFirst("<p>", "<p>"
                                + DateUtil.format(leResources.getCreateTime(), DatePattern.CHINESE_DATE_PATTERN) + " ");
                    } else {
                        context = DateUtil.format(leResources.getCreateTime(), DatePattern.CHINESE_DATE_PATTERN) + " "
                                + context;
                    }
                    list.add(context);
                }
            }
            if (CollectionUtil.isNotEmpty(list)) {
                try {
                    ByteArrayOutputStream word = RichTextToWordExporterUtils.exportRichTextToWord("普法工作信息",
                            SymbolConstant.LEFT_ROUND_BRACKETS + DateUtil.thisYear()
                                    + SymbolConstant.RIGHT_ROUND_BRACKETS,
                            orgName, list);
                    byte[] documentBytes = word.toByteArray();

                    // 设置响应内容类型
                    response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                    String zipName = "普法工作信息-" + DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN)
                            + ".docx";
                    String encodedFilename = URLEncoder.encode(zipName, "UTF-8");
                    response.setHeader("Content-Disposition", "attachment; filename=" + encodedFilename);
                    response.setContentLength(documentBytes.length);
                    response.getOutputStream().write(documentBytes);

                } catch (Exception e) {
                    throw new ServiceException(500, "导出失败");
                }

                // 写入响应
            }
        } else {
            throw new ServiceException(500, "没有可供导出的数据");
        }
    }

    @Override
    public void updateResourceColumn() {
        List<LeResources> list = this
                .list(new QueryWrapper<LeResources>().lambda().in(LeResources::getStatus, 1, 2, 5, 6));
        Map<String, String> columnIdMap = new HashMap<>();
        List<LeColumn> leColumnList = leColumnService
                .list(new QueryWrapper<LeColumn>().lambda().eq(LeColumn::getStatus, 0));
        if (CollectionUtil.isNotEmpty(leColumnList)) {
            for (LeColumn leColumn : leColumnList) {
                columnIdMap.put(leColumn.getColumnName(), leColumn.getId());
            }
        }
        if (CollectionUtil.isNotEmpty(list)) {
            for (LeResources leResources : list) {
                Set<String> set = new HashSet<>();
                Set<String> set1 = new HashSet<>();
                // 所有普法视频资源
                if ("31".equals(leResources.getType()) || "32".equals(leResources.getType())
                        || "33".equals(leResources.getType()) || "34".equals(leResources.getType())
                        || "41".equals(leResources.getType())) {
                    set.add(columnIdMap.get("普法视频"));
                    set1.add("普法视频");
                }
//                if ("0".equals(leResources.getSource())) {
//                    set.add(columnIdMap.get("资讯"));
//                    set1.add("资讯");
//                }
                if (leResources.getTitle().contains("主题公园") || leResources.getTitle().contains("文化广场")
                        || leResources.getTitle().contains("纪念馆") || leResources.getTitle().contains("文化园")
                        || leResources.getTitle().contains("基地") || leResources.getTitle().contains("学堂")
                        || leResources.getTitle().contains("研究院博物馆")) {
                    set.add(columnIdMap.get("普法阵地"));
                    set1.add("普法阵地");
                }
                if (leResources.getTitle().contains("以案释法")) {
                    set.add(columnIdMap.get("以案释法"));
                    set1.add("以案释法");
                }
                if (leResources.getTitle().contains("宣传活动") || leResources.getTitle().contains("活动")
                        || leResources.getTitle().contains("主题")) {
                    set.add(columnIdMap.get("法治文艺"));
                    set1.add("法治文艺");
                }
                if (leResources.getTitle().contains("动漫") || leResources.getTitle().contains("微动漫")
                        || leResources.getTitle().contains("动画")) {
                    set.add(columnIdMap.get("动漫"));
                    set1.add("动漫");
                }
                if (leResources.getTitle().contains("宣传片") || leResources.getTitle().contains("宪法")
                        || leResources.getTitle().contains("民法典") || leResources.getTitle().contains("视频")) {
                    set.add(columnIdMap.get("宣传片"));
                    set1.add("宣传片");
                }
                if (leResources.getTitle().contains("微电影")) {
                    set.add(columnIdMap.get("微电影"));
                    set1.add("微电影");
                }
                if (leResources.getTitle().contains("亚运")) {
                    set.add(columnIdMap.get("亚运专题"));
                    set1.add("亚运专题");
                }
                if (leResources.getTitle().contains("海报") || leResources.getTitle().contains("主题海报")
                        || "13".equals(leResources.getType())) {
                    set.add(columnIdMap.get("海报"));
                    set1.add("海报");
                }
                if (leResources.getTitle().contains("微课") || leResources.getTitle().contains("课堂")
                        || leResources.getTitle().contains("解读")) {
                    set.add(columnIdMap.get("微课"));
                    set1.add("微课");
                }
                // if (leResources.getTitle().contains("资讯")) {
                // set.add(columnIdMap.get("资讯"));
                // set1.add("资讯");
                // }
                if (leResources.getTitle().contains("法治乡村") || leResources.getTitle().contains("乡村")) {
                    set.add(columnIdMap.get("法治乡村"));
                    set1.add("法治乡村");
                }
                if (leResources.getTitle().contains("那些事") || leResources.getTitle().contains("尚法小课堂")
                        || leResources.getTitle().contains("小剧场")) {
                    set.add(columnIdMap.get("情景剧"));
                    set1.add("情景剧");
                }
                if (leResources.getTitle().contains("民法典")) {
                    set.add(columnIdMap.get("民法典"));
                    set1.add("民法典");
                }
                if (leResources.getTitle().contains("宪法")) {
                    set.add(columnIdMap.get("宪法"));
                    set1.add("宪法");
                }
                if (leResources.getTitle().contains("亚运")) {
                    set.add(columnIdMap.get("护航亚运 法治同行"));
                    set1.add("护航亚运 法治同行");
                }
                if (leResources.getTitle().contains("公务员") || leResources.getTitle().contains("后陈经验 ")) {
                    set.add(columnIdMap.get("乡镇公务员"));
                    set1.add("乡镇公务员");
                }
                if (leResources.getTitle().contains("信息")) {
                    set.add(columnIdMap.get("信息安全"));
                    set1.add("信息安全");
                }
                if (leResources.getTitle().contains("二十大精神")) {
                    set.add(columnIdMap.get("学习党的二十大精神"));
                    set1.add("学习党的二十大精神");
                }
                if (leResources.getTitle().contains("廉洁从政") || leResources.getTitle().contains("纪法小课堂")
                        || leResources.getTitle().contains("纪法微课堂")) {
                    set.add(columnIdMap.get("廉洁从政"));
                    set1.add("廉洁从政");
                }
                if (leResources.getTitle().contains("食品安全") || leResources.getTitle().contains("食品")) {
                    set.add(columnIdMap.get("食品安全"));
                    set1.add("食品安全");
                }
                if (leResources.getTitle().contains("防诈骗") || leResources.getTitle().contains("诈骗")) {
                    set.add(columnIdMap.get("防诈骗"));
                    set1.add("防诈骗");
                }
                if (leResources.getTitle().contains("法助共富") || leResources.getTitle().contains("共富")) {
                    set.add(columnIdMap.get("法助共富"));
                    set1.add("法助共富");
                }
                if (leResources.getTitle().contains("防疫") || leResources.getTitle().contains("防疫法宣")) {
                    set.add(columnIdMap.get("防疫法宣"));
                    set1.add("防疫法宣");
                }
                if (leResources.getTitle().contains("涉外")) {
                    set.add(columnIdMap.get("涉外"));
                    set1.add("涉外");
                }
                if (leResources.getTitle().contains("妇女儿童")) {
                    set.add(columnIdMap.get("妇女儿童"));
                    set1.add("妇女儿童");
                }
                if (leResources.getTitle().contains("老年人")) {
                    set.add(columnIdMap.get("老年人"));
                    set1.add("老年人");
                }
                if (leResources.getTitle().contains("青少年") || leResources.getTitle().contains("青春")
                        || leResources.getTitle().contains("校园")) {
                    set.add(columnIdMap.get("青少年"));
                    set1.add("青少年");
                }

                if (CollectionUtil.isNotEmpty(set)) {
                    if ("0".equals(leResources.getSource())) {
                        //set超过1个,且来源为0，就只配置为资讯，找对应的id
                        if (set.size() > 1) {
                            set.clear();
                            set.add(columnIdMap.get("资讯"));
                            set1.clear();
                            set1.add("资讯");
                            leResources.setColumnId(String.join(",", set));
                            leResources.setColumnName(String.join(",", set1));
                            leResources.setStatus(6);
                            this.updateById(leResources);
                            for (String columnId : set) {
                                if (ObjectUtil.isNotEmpty(columnId)) {
                                    addColumnResources(leResources, columnId);
                                }
                            }

                        } else if (!set1.contains("资讯")) {
                            leResources.setColumnId(String.join(",", set));
                            leResources.setColumnName(String.join(",", set1));
                            leResources.setStatus(6);
                            this.updateById(leResources);
                            for (String columnId : set) {
                                if (ObjectUtil.isNotEmpty(columnId)) {
                                    addColumnResources(leResources, columnId);
                                }
                            }
                        }
                    } else {
                        //set只有1个且为普法视频，就不要配置
                        if (set.size() == 1 && set1.contains("普法视频")) {
                            set.clear();
                            set1.clear();
                        }
                        if (CollectionUtil.isNotEmpty(set)) {
                            leResources.setColumnId(String.join(",", set));
                            leResources.setColumnName(String.join(",", set1));
                            leResources.setStatus(6);
                            this.updateById(leResources);
                            for (String columnId : set) {
                                if (ObjectUtil.isNotEmpty(columnId)) {
                                    addColumnResources(leResources, columnId);
                                }
                            }
                        }
                    }
                }

            }
        }
    }

    private void addColumnResources(LeResources resources, String columnId) {

        LeColumnResources model = new LeColumnResources();
        model.setColumnId(columnId);
        model.setTopic(0);
        model.setResourcesId(resources.getId());
        model.setSort(0);
        model.setCreateTime(DateUtil.date());

        model.setResourcesName(resources.getTitle());
        model.setResourcesType(resources.getType());
        model.setResourcesDeptId(resources.getDeptId());
        model.setResourcesDeptName(resources.getDeptName());
        model.setResourcesSource(resources.getSource());
        leColumnResourcesService.save(model);
    }

    @Override
    public void randomView() {
        List<LeResources> list = this.list(new QueryWrapper<LeResources>().lambda().in(LeResources::getStatus, 1, 2, 5, 6));
        if (CollectionUtil.isNotEmpty(list)) {
            for (LeResources leResources : list) {
                if (ObjectUtil.isEmpty(leResources.getViewNum())) {
                    leResources.setViewNum(RandomUtil.randomInt(50, 200));
                    this.updateById(leResources);
                }
            }
        }
    }

    /**
     * 批量获取指定时间段内的LeResources附件并下载到本地
     *
     * @param beginTime 开始时间，格式为yyyy-MM-dd
     * @param endTime   结束时间，格式为yyyy-MM-dd
     * @param localPath 本地保存路径
     * @param fileTypes 文件类型过滤，如jpg,png,mp4等，逗号分隔，为空则下载所有类型
     * @return 下载成功的文件数量
     * @throws IOException 下载过程中可能出现的IO异常
     */
    @Override
    public int batchDownloadAttachments(String beginTime, String endTime, String localPath, String fileTypes) throws IOException {
        // 参数检查
        if (ObjectUtil.isEmpty(localPath)) {
            throw new ServiceException(500, "本地保存路径不能为空");
        }

        // 创建本地目录
        java.io.File dir = new java.io.File(localPath);
        if (!dir.exists() && !dir.mkdirs()) {
            throw new ServiceException(500, "创建本地目录失败: " + localPath);
        }

        // 构建查询条件
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id,title");
        // 根据时间范围查询
        if (ObjectUtil.isAllNotEmpty(beginTime, endTime)) {
            queryWrapper.apply("date_format(create_time,'%Y-%m-%d') >= date_format('" + beginTime + "','%Y-%m-%d')")
                    .apply("date_format(create_time,'%Y-%m-%d') <= date_format('" + endTime + "','%Y-%m-%d')");
        }

        // 查询符合条件的普法资源
        List<LeResources> resourcesList = this.list(queryWrapper);
        if (CollectionUtil.isEmpty(resourcesList)) {
            return 0;
        }

        // 解析文件类型过滤条件，作为排除类型使用
        Set<String> excludedFileTypes = new HashSet<>();
        if (ObjectUtil.isNotEmpty(fileTypes)) {
            String[] types = fileTypes.toLowerCase().split(",");
            excludedFileTypes.addAll(Arrays.asList(types));
        }

        // 记录成功下载的文件数
        int successCount = 0;

        // 按资源分组整理数据，避免目录杂乱
        for (LeResources resource : resourcesList) {
            // 查询该资源的所有附件
            List<SysFileInfo> fileInfoList = sysFileInfoService.lambdaQuery()
                    .eq(SysFileInfo::getReferId, resource.getId())
                    .list();

            if (CollectionUtil.isEmpty(fileInfoList)) {
                continue; // 如果没有附件，跳过这个资源，不创建文件夹
            }

            // 筛选不在排除列表中的文件
            List<SysFileInfo> filteredFiles;
            if (!excludedFileTypes.isEmpty()) {
                filteredFiles = fileInfoList.stream()
                        .filter(file -> !excludedFileTypes.contains(file.getFileSuffix().toLowerCase()))
                        .collect(Collectors.toList());
            } else {
                filteredFiles = fileInfoList; // 如果没有指定排除类型，使用所有文件
            }

            if (CollectionUtil.isEmpty(filteredFiles)) {
                continue; // 如果过滤后没有文件，跳过这个资源，不创建文件夹
            }

            // 为每个资源创建独立子目录
            String resourceName = resource.getTitle();
            // 处理目录名中的特殊字符
            resourceName = resourceName.replaceAll("[\\\\/:*?\"<>|]", "_");
            String resourceDir = localPath + File.separator + resource.getId() + "_" + resourceName;

            java.io.File resourceDirFile = new java.io.File(resourceDir);
            if (!resourceDirFile.exists() && !resourceDirFile.mkdirs()) {
                continue; // 如果创建目录失败，跳过这个资源
            }

            // 下载附件
            for (SysFileInfo fileInfo : filteredFiles) {
                try {
                    // 构建本地文件路径
                    String fileName = fileInfo.getFileOriginName();
                    if (ObjectUtil.isEmpty(fileName)) {
                        fileName = fileInfo.getId() + "." + fileInfo.getFileSuffix();
                    } else if (!fileName.toLowerCase().endsWith("." + fileInfo.getFileSuffix().toLowerCase())) {
                        fileName = fileName + "." + fileInfo.getFileSuffix();
                    }

                    // 处理文件名中的特殊字符
                    fileName = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
                    String filePath = resourceDir + File.separator + fileName;

                    // 通过HTTP请求下载文件
                    java.net.URL url = new java.net.URL(OssSignedUrlUtil.generatePresignedUrlWithPublicDomain(fileInfo.getFilePath()));
                    java.net.HttpURLConnection conn = (java.net.HttpURLConnection) url.openConnection();
                    conn.setConnectTimeout(30000); // 30秒连接超时
                    conn.setReadTimeout(120000);   // 2分钟读取超时
                    conn.setRequestMethod("GET");

                    if (conn.getResponseCode() != 200) {
                        // 如果HTTP响应不是200，跳过该文件
                        continue;
                    }

                    // 写入本地文件
                    try (java.io.InputStream inputStream = conn.getInputStream();
                         FileOutputStream fos = new FileOutputStream(filePath);
                         BufferedOutputStream bos = new BufferedOutputStream(fos)) {

                        byte[] buffer = new byte[8192]; // 使用8KB缓冲区
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            bos.write(buffer, 0, bytesRead);
                        }
                        bos.flush();
                        successCount++;
                    }
                } catch (Exception e) {
                    // 记录错误但继续处理下一个文件
                    e.printStackTrace();
                }
            }
        }

        return successCount;
    }

    @Override
    public PageResult<JSONObject> lawReportPage(String areaName) {
        Page<JSONObject> positionPage = this.baseMapper.lawReportPage(PageFactory.defaultPage(),areaName);

        return new PageResult<>(positionPage);
    }

    @Override
    public List<String> selectHasLabel() {
        return this.baseMapper.selectHasLabel();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOldDataForClassification() {
        // 获取面向人群字典数据 (pfzy-mxrq)
        Map<String, String> audienceDict = getDictValueMap("pfzy-mxrq");

        // 获取主题分类字典数据 (pfzy_ztfl)
        Map<String, String> themeDict = getDictValueMap("pfzy_ztfl");

        if (audienceDict.isEmpty() && themeDict.isEmpty()) {
            throw new ServiceException(500, "未找到相关字典配置，请检查字典代码：pfzy-mxrq 和 pfzy_ztfl");
        }

        // 查询所有需要处理的资源数据（主题分类或面向人群分类为空的数据）
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .and(wrapper -> wrapper
                    .and(w1 -> w1.isNull(LeResources::getThemeType).or().eq(LeResources::getThemeType, ""))
                    .or(w2 -> w2.isNull(LeResources::getAudienceType).or().eq(LeResources::getAudienceType, ""))
                )
                .ne(LeResources::getStatus, 9); // 排除已删除的数据
        queryWrapper.lambda().eq(LeResources::getSource, "1");
        List<LeResources> resourcesList = this.list(queryWrapper);

        if (CollectionUtil.isEmpty(resourcesList)) {
            return;
        }

        int processedCount = 0;
        int audienceUpdatedCount = 0;
        int themeUpdatedCount = 0;

        for (LeResources resource : resourcesList) {
            boolean needUpdate = false;
            String searchText = (ObjectUtil.isEmpty(resource.getTitle()) ? "" : resource.getTitle()) + " " +
                               (ObjectUtil.isEmpty(resource.getContext()) ? "" : stripHtmlTags(resource.getContext()));

            // 处理面向人群分类 - 支持多选匹配
            if (ObjectUtil.isEmpty(resource.getAudienceType()) && !audienceDict.isEmpty()) {
                List<String> matchedAudienceCodes = matchMultipleDictValuesFromText(searchText, audienceDict);
                if (!matchedAudienceCodes.isEmpty()) {
                    resource.setAudienceType(String.join(",", matchedAudienceCodes));
                    needUpdate = true;
                    audienceUpdatedCount++;
                }
            }

            // 处理主题分类 - 支持多选匹配
            if (ObjectUtil.isEmpty(resource.getThemeType()) && !themeDict.isEmpty()) {
                List<String> matchedThemeCodes = matchMultipleDictValuesFromText(searchText, themeDict);
                if (!matchedThemeCodes.isEmpty()) {
                    resource.setThemeType(String.join(",", matchedThemeCodes));
                    needUpdate = true;
                    themeUpdatedCount++;
                }
            }

            // 只要有任何一个字段更新就保存
            if (needUpdate) {
                this.updateById(resource);
                processedCount++;
            }
        }

        System.out.println("旧数据处理完成！");
        System.out.println("总处理记录数：" + processedCount);
        System.out.println("面向人群分类更新数：" + audienceUpdatedCount);
        System.out.println("主题分类更新数：" + themeUpdatedCount);
    }

    /**
     * 通过旧标签数据处理分类信息
     * 从law_education_resources_label_old和law_education_resources_label_contact_old表中获取标签信息
     * 然后根据标签名称匹配字典来更新资源的人群分类和主题分类
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOldDataForClassificationByLabel() {
        // 获取面向人群字典数据 (pfzy-mxrq)
        Map<String, String> audienceDict = getDictValueMap("pfzy-mxrq");

        // 获取主题分类字典数据 (pfzy_ztfl)
        Map<String, String> themeDict = getDictValueMap("pfzy_ztfl");

        if (audienceDict.isEmpty() && themeDict.isEmpty()) {
            throw new ServiceException(500, "未找到相关字典配置，请检查字典代码：pfzy-mxrq 和 pfzy_ztfl");
        }

        // 查询所有需要处理的资源数据（主题分类或面向人群分类为空的数据）
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .and(wrapper -> wrapper
                    .and(w1 -> w1.isNull(LeResources::getThemeType).or().eq(LeResources::getThemeType, ""))
                    .or(w2 -> w2.isNull(LeResources::getAudienceType).or().eq(LeResources::getAudienceType, ""))
                )
                .ne(LeResources::getStatus, 9); // 排除已删除的数据
        queryWrapper.lambda().eq(LeResources::getSource, "1");
        List<LeResources> resourcesList = this.list(queryWrapper);

        if (CollectionUtil.isEmpty(resourcesList)) {
            System.out.println("没有找到需要处理的资源数据");
            return;
        }

        int processedCount = 0;
        int audienceUpdatedCount = 0;
        int themeUpdatedCount = 0;

        for (LeResources resource : resourcesList) {
            boolean needUpdate = false;

            // 获取资源关联的旧标签名称
            List<String> labelNames = this.baseMapper.selectOldLabelNamesByResourceId(resource.getId());

            if (CollectionUtil.isEmpty(labelNames)) {
                continue; // 如果没有标签，跳过
            }

            // 将所有标签名称合并为搜索文本
            String labelText = String.join(" ", labelNames);

            // 处理面向人群分类 - 支持多选匹配
            if (ObjectUtil.isEmpty(resource.getAudienceType()) && !audienceDict.isEmpty()) {
                List<String> matchedAudienceCodes = matchMultipleDictValuesFromText(labelText, audienceDict);
                if (!matchedAudienceCodes.isEmpty()) {
                    resource.setAudienceType(String.join(",", matchedAudienceCodes));
                    needUpdate = true;
                    audienceUpdatedCount++;
                }
            }

            // 处理主题分类 - 支持多选匹配
            if (ObjectUtil.isEmpty(resource.getThemeType()) && !themeDict.isEmpty()) {
                List<String> matchedThemeCodes = matchMultipleDictValuesFromText(labelText, themeDict);
                if (!matchedThemeCodes.isEmpty()) {
                    resource.setThemeType(String.join(",", matchedThemeCodes));
                    needUpdate = true;
                    themeUpdatedCount++;
                }
            }

            // 只要有任何一个字段更新就保存
            if (needUpdate) {
                this.updateById(resource);
                processedCount++;
            }
        }

        System.out.println("通过旧标签数据处理完成！");
        System.out.println("总处理记录数：" + processedCount);
        System.out.println("面向人群分类更新数：" + audienceUpdatedCount);
        System.out.println("主题分类更新数：" + themeUpdatedCount);
    }

    /**
     * 通过新标签规则处理标签关联
     * 包含两种处理方式：1.按原标签对应 2.按标题关键字对应
     * 往law_education_resources_label_contact表中添加数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processDataByNewLabelRules() {
        // 查询所有需要处理的资源数据
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .ne(LeResources::getStatus, 9) // 排除已删除的数据
                .eq(LeResources::getSource, "0"); // 只处理资源库数据
        List<LeResources> resourcesList = this.list(queryWrapper);

        if (CollectionUtil.isEmpty(resourcesList)) {
            System.out.println("没有找到需要处理的资源数据");
            return;
        }

        // 获取标签名称到ID的映射
        Map<String, String> labelNameToIdMap = getLabelNameToIdMap();

        int processedCount = 0;
        int labelMappingCount = 0;
        int titleKeywordCount = 0;

        for (LeResources resource : resourcesList) {
            Set<String> matchedLabelIds = new HashSet<>(); // 使用Set避免重复

            // 方式1：按原标签对应
            List<String> oldLabelNames = this.baseMapper.selectOldLabelNamesByResourceId(resource.getId());
            if (CollectionUtil.isNotEmpty(oldLabelNames)) {
                for (String oldLabelName : oldLabelNames) {
                    LabelMappingEnum labelMapping = LabelMappingEnum.findByOldLabelName(oldLabelName);
                    if (labelMapping != null) {
                        String newLabelName = labelMapping.getNewLabelName();
                        String labelId = labelNameToIdMap.get(newLabelName);
                        if (ObjectUtil.isNotEmpty(labelId)) {
                            matchedLabelIds.add(labelId);
                            labelMappingCount++;
                        }
                    }
                }
            }

            // 方式2：按标题关键字对应
            if (ObjectUtil.isNotEmpty(resource.getTitle())) {
                List<LabelMappingEnum> matchedEnums = LabelMappingEnum.findByTitleKeywords(resource.getTitle());
                for (LabelMappingEnum labelMapping : matchedEnums) {
                    String newLabelName = labelMapping.getNewLabelName();
                    String labelId = labelNameToIdMap.get(newLabelName);
                    if (ObjectUtil.isNotEmpty(labelId)) {
                        if (matchedLabelIds.add(labelId)) { // add方法返回true表示是新添加的
                            titleKeywordCount++;
                        }
                    }
                }
            }

            // 如果找到匹配的标签，则更新标签关联
            if (!matchedLabelIds.isEmpty()) {
                lerLabelService.updateLabelContact(resource.getId(), new ArrayList<>(matchedLabelIds));
                processedCount++;
            }
        }

        System.out.println("通过新标签规则处理完成！");
        System.out.println("总处理记录数：" + processedCount);
        System.out.println("按原标签对应匹配数：" + labelMappingCount);
        System.out.println("按标题关键字匹配数：" + titleKeywordCount);
    }



    /**
     * 获取标签名称到ID的映射
        Map<String, String> keywordRules = new HashMap<>();

        // 根据您提供的表格，按标题关键字对应（关键字 -> 新标签名称）
        keywordRules.put("习近平", "宣贯习近平法治思想");
        keywordRules.put("普法骨干", "普法骨干培训");
        keywordRules.put("党内", "党内法规宣传");
        keywordRules.put("宪法", "宪法宣传");
        keywordRules.put("民法典", "民法典普法");
        keywordRules.put("营商环境", "优化法治化营商环境专项普法");
        keywordRules.put("服务大局", "服务大局普法行活动");
        keywordRules.put("普法宣传", "其他普法宣传活动");
        keywordRules.put("公务员", "国家工作人员");
        keywordRules.put("法律明白人", "“法律明白人”队伍建设");
        keywordRules.put("宗教", "宗教人士");
        keywordRules.put("村民", "村民");
        keywordRules.put("公民法治素养", "其他");
        keywordRules.put("法治文化", "中华优秀传统法律文化和红色法治文化");
        keywordRules.put("社会主义", "社会主义法治文艺");
        keywordRules.put("民主法治村", "民主法治村（社区）建设");
        keywordRules.put("民主法治示范村", "民主法治村（社区）建设");
        keywordRules.put("乡村依法治理", "乡村依法治理");
        keywordRules.put("普法责任制", "落实普法责任制");
        keywordRules.put("八五", "“八五”普法总结验收");
        keywordRules.put("九五", "“九五”普法规划谋划");

        // 将标签名称转换为标签ID
        Map<String, String> labelNameToIdMap = getLabelNameToIdMap();
        Map<String, String> finalKeywordRules = new HashMap<>();

        for (Map.Entry<String, String> entry : keywordRules.entrySet()) {
            String keyword = entry.getKey();
            String labelName = entry.getValue();
            String labelId = labelNameToIdMap.get(labelName);

            if (ObjectUtil.isNotEmpty(labelId)) {
                finalKeywordRules.put(keyword, labelId);
            }
        }

        return finalKeywordRules;
    }

    /**
     * 获取标签名称到ID的映射
     * 从数据库中查询所有有效标签，建立名称到ID的映射关系
     */
    private Map<String, String> getLabelNameToIdMap() {
        Map<String, String> labelNameToIdMap = new HashMap<>();

        // 查询所有有效的标签
        QueryWrapper<LerLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(LerLabel::getStatus, 1); // 只查询有效标签
        List<LerLabel> labelList = lerLabelService.list(queryWrapper);

        if (CollectionUtil.isNotEmpty(labelList)) {
            for (LerLabel label : labelList) {
                if (ObjectUtil.isNotEmpty(label.getName())) {
                    labelNameToIdMap.put(label.getName(), label.getId());
                }
            }
        }

        return labelNameToIdMap;
    }

    /**
     * 获取字典值映射
     * @param dictTypeCode 字典类型代码
     * @return 字典值映射 (中文值 -> 代码)
     */
    private Map<String, String> getDictValueMap(String dictTypeCode) {
        Map<String, String> dictMap = new HashMap<>();

        try {
            // 查询字典类型
            QueryWrapper<SysDictType> typeQueryWrapper = new QueryWrapper<>();
            typeQueryWrapper.lambda().eq(SysDictType::getCode, dictTypeCode)
                    .eq(SysDictType::getStatus, 0);
            SysDictType dictType = sysDictTypeService.getOne(typeQueryWrapper);

            if (dictType == null) {
                System.out.println("未找到字典类型：" + dictTypeCode);
                return dictMap;
            }

            // 查询字典数据
            List<SysDictData> dictDataList = sysDictDataService.listByDictTypeCode(dictTypeCode);

            if (CollectionUtil.isNotEmpty(dictDataList)) {
                for (SysDictData dictData : dictDataList) {
                    // 优先使用备注字段，如果备注为空则使用value字段
                    String displayValue = (dictData.getRemark() != null && !dictData.getRemark().trim().isEmpty())
                            ? dictData.getRemark()
                            : dictData.getValue();
                    // 将中文值作为key，代码作为value
                    dictMap.put(displayValue, dictData.getCode());
                }
            }

        } catch (Exception e) {
            System.err.println("获取字典数据失败：" + dictTypeCode + "，错误：" + e.getMessage());
        }

        return dictMap;
    }

    /**
     * 匹配字典值
     * @param title 标题
     * @param context 内容
     * @param dictMap 字典映射
     * @return 匹配到的字典代码，如果没有匹配到则返回null
     */
    private String matchDictValue(String title, String context, Map<String, String> dictMap) {
        if (dictMap.isEmpty()) {
            return null;
        }

        // 将标题和内容合并进行匹配
        String searchText = (ObjectUtil.isEmpty(title) ? "" : title) + " " +
                           (ObjectUtil.isEmpty(context) ? "" : stripHtmlTags(context));

        return matchDictValueFromText(searchText, dictMap);
    }

    /**
     * 从文本中匹配字典值
     * @param searchText 搜索文本
     * @param dictMap 字典映射
     * @return 匹配到的字典代码，如果没有匹配到则返回null
     */
    private String matchDictValueFromText(String searchText, Map<String, String> dictMap) {
        if (dictMap.isEmpty() || ObjectUtil.isEmpty(searchText)) {
            return null;
        }

        // 遍历字典值，查找匹配项
        for (Map.Entry<String, String> entry : dictMap.entrySet()) {
            String dictValue = entry.getKey(); // 中文值
            String dictCode = entry.getValue(); // 代码

            // 在文本中查找字典中文值
            if (searchText.contains(dictValue)) {
                return dictCode;
            }
        }

        return null;
    }

    /**
     * 去除HTML标签
     * @param htmlText HTML文本
     * @return 纯文本
     */
    private String stripHtmlTags(String htmlText) {
        if (ObjectUtil.isEmpty(htmlText)) {
            return "";
        }

        // 去除所有HTML标签
        String plainText = htmlText.replaceAll("<[^>]+>", "");
        // 去除HTML转义字符
        plainText = plainText.replaceAll("&nbsp;", " ")
                            .replaceAll("&amp;", "&")
                            .replaceAll("&lt;", "<")
                            .replaceAll("&gt;", ">")
                            .replaceAll("&quot;", "\"")
                            .replaceAll("&#39;", "'");

        return plainText.trim();
    }

    /**
     * 从文本中匹配多个字典值（支持多选）
     * @param searchText 搜索文本
     * @param dictMap 字典映射
     * @return 匹配到的字典代码列表
     */
    private List<String> matchMultipleDictValuesFromText(String searchText, Map<String, String> dictMap) {
        List<String> matchedCodes = new ArrayList<>();

        if (dictMap.isEmpty() || ObjectUtil.isEmpty(searchText)) {
            return matchedCodes;
        }

        // 遍历字典值，查找所有匹配项
        for (Map.Entry<String, String> entry : dictMap.entrySet()) {
            String dictValue = entry.getKey(); // 中文值
            String dictCode = entry.getValue(); // 代码

            // 在文本中查找字典中文值
            if (searchText.contains(dictValue)) {
                matchedCodes.add(dictCode);
            }
        }

        return matchedCodes;
    }

    @Override
    public Object importExcelAndPath(String excelPath, String fileFolderPath) {
        //读取本地excel文件并转为leResources对象
        List<LeResources> leResourcesList = PoiUtil.importExcel(excelPath,0,0, LeResources.class);
        assert leResourcesList != null;
        for (LeResources leResources : leResourcesList) {
            //上传文件到zlpf
            uploadVideoFile(fileFolderPath);
        }


        return null;
    }

    /**
     * 上传本地视频文件到指定接口
     *
     * @param localVideoPath 本地视频文件路径
     * @return 上传结果，包含成功状态和文件信息
     */
    public ResponseData uploadVideoFile(String localVideoPath) {
        log.info("开始上传视频文件，本地路径: {}", localVideoPath);

        try {
            // 1. 参数验证
            if (ObjectUtil.isEmpty(localVideoPath)) {
                log.error("本地视频文件路径不能为空");
                throw new ServiceException(500, "本地视频文件路径不能为空");
            }

            // 2. 检查文件是否存在
            File videoFile = new File(localVideoPath);
            if (!videoFile.exists()) {
                log.error("视频文件不存在: {}", localVideoPath);
                throw new ServiceException(500, "视频文件不存在: " + localVideoPath);
            }

            if (!videoFile.isFile()) {
                log.error("指定路径不是文件: {}", localVideoPath);
                throw new ServiceException(500, "指定路径不是文件: " + localVideoPath);
            }

            // 3. 验证文件类型（支持常见视频格式）
            String fileName = videoFile.getName().toLowerCase();
            if (!isValidVideoFile(fileName)) {
                log.error("不支持的视频文件格式: {}", fileName);
                throw new ServiceException(500, "不支持的视频文件格式，仅支持mp4、avi、mov、wmv、flv、mkv格式");
            }

            // 4. 检查文件大小（限制500MB）
            long fileSize = videoFile.length();
            long maxSize = 500 * 1024 * 1024; // 500MB
            if (fileSize > maxSize) {
                log.error("视频文件大小超过限制: {}MB，最大支持500MB", fileSize / 1024 / 1024);
                throw new ServiceException(500, "视频文件大小超过限制，最大支持500MB");
            }

            log.info("视频文件验证通过，文件大小: {}MB", fileSize / 1024 / 1024);

            // 5. 将本地文件转换为MultipartFile
            MultipartFile multipartFile = convertFileToMultipartFile(videoFile);

            // 6. 调用上传接口
            String uploadUrl = "https://zlpf.zjsft.gov.cn/api/sysFileInfo/upload";
            ResponseEntity<String> response = FileUploaderUtils.uploadFile(uploadUrl, multipartFile);

            // 7. 处理上传结果
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.info("视频文件上传成功，响应: {}", responseBody);

                // 解析响应结果
                JSONObject jsonResponse = JSONObject.parseObject(responseBody);
                Boolean success = jsonResponse.getBoolean("success");

                if (success != null && success) {
                    // 上传成功，返回文件信息
                    SysFileInfo fileInfo = JSONObject.parseObject(jsonResponse.getString("data"), SysFileInfo.class);
                    log.info("视频文件上传成功，文件ID: {}, 文件名: {}", fileInfo.getId(), fileInfo.getFileOriginName());

                    return ResponseData.success(fileInfo);
                } else {
                    // 上传失败
                    String message = jsonResponse.getString("message");
                    log.error("视频文件上传失败: {}", message);
                    throw new ServiceException(500, "视频文件上传失败: " + message);
                }
            } else {
                log.error("视频文件上传失败，HTTP状态码: {}, 响应: {}", response.getStatusCodeValue(), response.getBody());
                throw new ServiceException(500, "视频文件上传失败，HTTP状态码: " + response.getStatusCodeValue());
            }

        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("视频文件上传过程中发生异常: {}", e.getMessage(), e);
            throw new ServiceException(500, "视频文件上传过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 验证是否为支持的视频文件格式
     *
     * @param fileName 文件名
     * @return 是否为支持的视频格式
     */
    private boolean isValidVideoFile(String fileName) {
        if (ObjectUtil.isEmpty(fileName)) {
            return false;
        }

        String[] supportedFormats = {"mp4", "avi", "mov", "wmv", "flv", "mkv", "m4v", "3gp", "webm"};
        String fileExtension = "";

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            fileExtension = fileName.substring(lastDotIndex + 1).toLowerCase();
        }

        return Arrays.asList(supportedFormats).contains(fileExtension);
    }

    /**
     * 将本地文件转换为MultipartFile对象
     *
     * @param file 本地文件
     * @return MultipartFile对象
     * @throws IOException 文件读取异常
     */
    private MultipartFile convertFileToMultipartFile(File file) throws IOException {
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            byte[] fileBytes = IoUtil.readBytes(fileInputStream);

            // 获取文件的MIME类型
            String contentType = getVideoContentType(file.getName());

            return new CustomMultipartFile(
                fileBytes,
                file.getName(),
                file.getName(),
                contentType
            );
        }
    }

    /**
     * 根据文件名获取视频文件的MIME类型
     *
     * @param fileName 文件名
     * @return MIME类型
     */
    private String getVideoContentType(String fileName) {
        if (ObjectUtil.isEmpty(fileName)) {
            return "application/octet-stream";
        }

        String extension = "";
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            extension = fileName.substring(lastDotIndex + 1).toLowerCase();
        }

        switch (extension) {
            case "mp4":
                return "video/mp4";
            case "avi":
                return "video/x-msvideo";
            case "mov":
                return "video/quicktime";
            case "wmv":
                return "video/x-ms-wmv";
            case "flv":
                return "video/x-flv";
            case "mkv":
                return "video/x-matroska";
            case "m4v":
                return "video/x-m4v";
            case "3gp":
                return "video/3gpp";
            case "webm":
                return "video/webm";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 自定义MultipartFile实现类
     */
    private static class CustomMultipartFile implements MultipartFile {
        private final byte[] fileBytes;
        private final String originalFilename;
        private final String name;
        private final String contentType;

        public CustomMultipartFile(byte[] fileBytes, String originalFilename, String name, String contentType) {
            this.fileBytes = fileBytes;
            this.originalFilename = originalFilename;
            this.name = name;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return this.name;
        }

        @Override
        public String getOriginalFilename() {
            return this.originalFilename;
        }

        @Override
        public String getContentType() {
            return this.contentType;
        }

        @Override
        public boolean isEmpty() {
            return this.fileBytes == null || this.fileBytes.length == 0;
        }

        @Override
        public long getSize() {
            return this.fileBytes != null ? this.fileBytes.length : 0;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return this.fileBytes;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(this.fileBytes);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(this.fileBytes);
            }
        }
    }
}
