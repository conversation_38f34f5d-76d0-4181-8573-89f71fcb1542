package com.concise.modular.laweducation.controller;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.consts.CommonConstant;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.modular.laweducation.entity.LeResources;
import com.concise.modular.laweducation.param.LeResourcesParam;
import com.concise.modular.laweducation.service.LeResourcesService;
import com.concise.modular.laweducation.service.LerAuditService;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

/**
 * 普法资源(信息)控制器
 *
 * <AUTHOR>
 * @date 2024-03-25 14:38:22
 */
@Api(tags = "普法资源(信息)")
@RestController
public class LeResourcesController {

    @Resource
    private LeResourcesService leResourcesService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private LerAuditService lerAuditService;

    private static final int MAX_HISTORY_SIZE = 10;

    /**
     * 查询普法资源(信息)
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    @PostMapping("/leResources/page")
    @ApiOperation("普法资源(信息)_分页查询")
    public ResponseData page(@RequestBody LeResourcesParam leResourcesParam) {
        return new SuccessResponseData(leResourcesService.page(leResourcesParam));
    }

    /**
     * 普法资源库查询
     *
     * @param leResourcesParam
     * @return
     */
    @PostMapping("/leResources/store/page")
    @ApiOperation("普法资源(信息)库_查询")
    public ResponseData storePage(@RequestBody LeResourcesParam leResourcesParam) {
        return new SuccessResponseData(leResourcesService.storePage(leResourcesParam));
    }

    @GetMapping("/leResources/mobilePage")
    @ApiOperation("普法资源(信息)_移动端分页查询")
    public ResponseData mobilePage(LeResourcesParam leResourcesParam) {
        if (ObjectUtil.isNotEmpty(leResourcesParam.getTitle())) {
            String key = CommonConstant.RESOURCE_SEARCH_CACHE + LoginContextHolder.me().getSysLoginUser().getId();
            List<String> range = stringRedisTemplate.opsForList().range(key, 0, MAX_HISTORY_SIZE);
            if (ObjectUtil.isEmpty(range)) {
                range = Collections.emptyList();
            }
            // 检查是否已经存在
            if (!range.contains(leResourcesParam.getTitle())) {
                stringRedisTemplate.opsForList().leftPush(key, leResourcesParam.getTitle());
                stringRedisTemplate.opsForList().trim(key, 0, 9);
            }

        }
        PageResult<LeResources> page = leResourcesService.mobilePage(leResourcesParam);
        return new SuccessResponseData(page);
    }

    /**
     * 移动端推荐资源
     */
    @GetMapping("/leResources/recommend")
    @ApiOperation("普法资源(信息)_移动端推荐资源")
    public ResponseData recommend(@RequestParam(required = false, defaultValue = "1823254908087398401") String pid) {
        return new SuccessResponseData(leResourcesService.recommend(pid));
    }

    /**
     * 个人搜索纪录列表
     */
    @GetMapping("/leResources/searchRecord")
    @ApiOperation("普法资源(信息)_个人搜索纪录列表")
    public ResponseData searchRecord() {
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        return new SuccessResponseData(stringRedisTemplate.opsForList().range(CommonConstant.RESOURCE_SEARCH_CACHE + sysLoginUser.getId(), 0, MAX_HISTORY_SIZE));
    }

    /**
     * 添加普法资源(信息)
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    @PostMapping("/leResources/add")
    @ApiOperation("普法资源(信息)_增加")
    @BusinessLog(title = "普法资源(信息)_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody LeResourcesParam leResourcesParam) {
        leResourcesService.add(leResourcesParam);
        return new SuccessResponseData();
    }

    /**
     * 普法资源(信息)_移动端点赞/收藏/转发/浏览
     *
     * @param id
     * @param type 1 浏览 2 点赞 3 收藏 4 转发
     * @return
     */
    @GetMapping("/leResources/viewById")
    @ApiOperation("普法资源(信息)_移动端(包含取消)点赞/收藏/转发/浏览")
    @BusinessLog(title = "普法资源(信息)_移动端(包含取消)点赞/收藏/转发/浏览", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData viewById(@RequestParam String id, @RequestParam Integer type) {
        leResourcesService.viewById(id, type);
        return new SuccessResponseData();
    }

    /**
     * 删除普法资源(信息)
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    @PostMapping("/leResources/delete")
    @ApiOperation("普法资源(信息)_删除")
    @BusinessLog(title = "普法资源(信息)_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody LeResourcesParam leResourcesParam) {
        leResourcesParam.getIdList().forEach(id -> leResourcesService.delete(id, "user"));
        return new SuccessResponseData();
    }

    @PostMapping("/leResources/withdraw")
    @ApiOperation("普法资源(信息)_撤回")
    @BusinessLog(title = "普法资源(信息)_撤回", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData withdraw(@RequestBody @Validated(LeResourcesParam.delete.class) LeResourcesParam param) {
        leResourcesService.withdraw(param.getId());
        return new SuccessResponseData();
    }

    @PostMapping("/leResources/audit")
    @ApiOperation("普法资源(信息)_审核")
    @BusinessLog(title = "普法资源(信息)_审核", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData audit(@RequestBody LeResourcesParam param) {
        param.setDeptId(null);
        param.setDeptName(null);
        leResourcesService.audit(param);
        return new SuccessResponseData();
    }

    @PostMapping("/leResources/merge")
    @ApiOperation("普法资源(信息)_合并审核")
    @BusinessLog(title = "普法资源(信息)_合并审核", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData merge(@RequestBody @Validated(LeResourcesParam.add.class) LeResourcesParam leResourcesParam) {
        leResourcesService.merge(leResourcesParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑普法资源(信息)
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    @PostMapping("/leResources/edit")
    @ApiOperation("普法资源(信息)_编辑")
    @BusinessLog(title = "普法资源(信息)_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody LeResourcesParam leResourcesParam) {
        leResourcesService.edit(leResourcesParam);
        return new SuccessResponseData();
    }

    /**
     * 查看普法资源(信息)
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    @GetMapping("/leResources/detail")
    @ApiOperation("普法资源(信息)_查看")
    public ResponseData detail(@Validated(LeResourcesParam.detail.class) LeResourcesParam leResourcesParam) {
        return new SuccessResponseData(leResourcesService.detail(leResourcesParam));
    }

    /**
     * 普法资源(信息)列表
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    @GetMapping("/leResources/list")
    @ApiOperation("普法资源(信息)_列表")
    public ResponseData list(LeResourcesParam leResourcesParam) {
        return new SuccessResponseData(leResourcesService.list(leResourcesParam));
    }

    @GetMapping("/leResources/export")
    @ApiOperation("普法资源(信息)_导出")
    @BusinessLog(title = "普法资源(信息)_导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void export(LeResourcesParam leResourcesParam) {
        leResourcesService.export(leResourcesParam);
    }

    /**
     * 导出图片压缩包
     */
    @GetMapping("/leResources/exportPic")
    @ApiOperation("普法资源(信息)_导出图片压缩包")
    @BusinessLog(title = "普法资源(信息)_导出图片压缩包", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void exportPic(LeResourcesParam leResourcesParam) {
        // 限制只能导出1个月时间范围的
        if (ObjectUtil.isAllNotEmpty(leResourcesParam.getSearchBeginTime(), leResourcesParam.getSearchEndTime())) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
                java.util.Date begin = sdf.parse(leResourcesParam.getSearchBeginTime());
                java.util.Date end = sdf.parse(leResourcesParam.getSearchEndTime());
                long diff = end.getTime() - begin.getTime();
                if (diff < 0 || diff > 31L * 24 * 60 * 60 * 1000) {
                    throw new RuntimeException("仅允许导出1个月范围内的数据");
                }
            } catch (java.text.ParseException e) {
                throw new RuntimeException("时间格式错误，应为yyyy-MM-dd");
            }
        }
        leResourcesService.exportPic(leResourcesParam);
    }

    /**
     * 导出word
     */
    @GetMapping("/leResources/exportWord")
    @ApiOperation("普法资源(信息)_导出word")
    @BusinessLog(title = "普法资源(信息)_导出word", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void exportWord(LeResourcesParam leResourcesParam) {
        leResourcesService.exportWord(leResourcesParam);
    }

    /**
     * 随机刷50-200之间的阅读量
     */
    @GetMapping("/leResources/randomView")
    @ApiOperation("普法资源(信息)_随机刷50-200之间的阅读量")
    @BusinessLog(title = "普法资源(信息)_随机刷50-200之间的阅读量", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData randomView() {
        leResourcesService.randomView();
        return new SuccessResponseData();
    }

    /**
     * 批量补全流程的机构数据
     */
    @GetMapping("/leResources/batchHandleOrg")
    @ApiOperation("普法资源(信息)_批量处理机构不全数据")
    @BusinessLog(title = "普法资源(信息)_批量处理机构不全数据", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData batchHandleOrg() {
        lerAuditService.batchHandleOrg();
        return new SuccessResponseData();
    }

    /**
     * 批量获取并下载指定时间段内的普法资源附件
     *
     * @param beginTime 开始时间，格式为yyyy-MM-dd
     * @param endTime   结束时间，格式为yyyy-MM-dd
     * @param localPath 本地保存路径
     * @param fileTypes 文件类型过滤，如jpg,png,mp4等，逗号分隔，为空则下载所有类型
     * @return 下载成功的文件数量
     */
    @GetMapping("/leResources/batchDownloadAttachments")
    @ApiOperation("普法资源(信息)_批量下载附件到本地")
    @BusinessLog(title = "普法资源(信息)_批量下载附件到本地", opType = LogAnnotionOpTypeEnum.EXPORT)
    public ResponseData batchDownloadAttachments(
            @RequestParam(required = true) String beginTime,
            @RequestParam(required = true) String endTime,
            @RequestParam(required = true) String localPath,
            @RequestParam(required = false) String fileTypes) {
        try {
            int count = leResourcesService.batchDownloadAttachments(beginTime, endTime, localPath, fileTypes);
            return new SuccessResponseData("成功下载" + count + "个文件");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error("下载失败: " + e.getMessage());
        }
    }

    /**
     * 处理旧数据，根据标题和内容匹配字典值更新主题分类和面向人群分类
     */
    @GetMapping("/leResources/processOldDataForClassification")
    @ApiOperation("普法资源(信息)_处理旧数据分类")
    @BusinessLog(title = "普法资源(信息)_处理旧数据分类", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData processOldDataForClassification() {
        try {
            leResourcesService.processOldDataForClassification();
            return new SuccessResponseData("旧数据处理完成");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 通过旧标签数据处理分类信息
     */
    @GetMapping("/leResources/processOldDataForClassificationByLabel")
    @ApiOperation("普法资源(信息)_通过旧标签处理数据分类")
    @BusinessLog(title = "普法资源(信息)_通过旧标签处理数据分类", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData processOldDataForClassificationByLabel() {
        try {
            leResourcesService.processOldDataForClassificationByLabel();
            return new SuccessResponseData("通过旧标签数据处理完成");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 通过新标签规则处理标签关联
     */
    @GetMapping("/leResources/processDataByNewLabelRules")
    @ApiOperation("普法资源(信息)_通过新标签规则处理标签关联")
    @BusinessLog(title = "普法资源(信息)_通过新标签规则处理标签关联", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData processDataByNewLabelRules() {
        try {
            leResourcesService.processDataByNewLabelRules();
            return new SuccessResponseData("通过新标签规则处理完成");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件路径和excel路径来导入普法资源
     */
    @PostMapping("/leResources/importExcelAndPath")
    @ApiOperation("普法资源(信息)_导入Excel")
    @BusinessLog(title = "普法资源(信息)_导入Excel", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData importExcelAndPath(@RequestParam String excelPath, @RequestParam String fileFolderPath) {
        return new SuccessResponseData(leResourcesService.importExcelAndPath(excelPath, fileFolderPath));
    }

}
