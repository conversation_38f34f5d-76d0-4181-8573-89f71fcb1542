package com.concise.modular.laweducation.service;

import java.io.IOException;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.home.vo.HomeResourceVo;
import com.concise.modular.laweducation.entity.LeColumn;
import com.concise.modular.laweducation.entity.LeResources;
import com.concise.modular.laweducation.param.LeResourcesParam;

/**
 * 普法资源(信息)service接口
 *
 * <AUTHOR>
 * @date 2024-03-25 14:38:22
 */
public interface LeResourcesService extends IService<LeResources> {

    /**
     * 查询普法资源(信息)
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    PageResult<LeResources> page(LeResourcesParam leResourcesParam);

    /**
     * 分页
     * @param page
     * @param leResourcesParam
     * @return
     */

    /**
     * 普法资源(信息)列表
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    List<LeResources> list(LeResourcesParam leResourcesParam);

    /**
     * 添加普法资源(信息)
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    void add(LeResourcesParam leResourcesParam);

    /**
     * 普法责任制新增普法资源
     *
     * @param leResourcesParam
     */
    void addResource(LeResourcesParam leResourcesParam);

    /**
     * 删除普法资源(信息)
     *
     * @param id     id
     * @param reason user:用户删除 label：标签关联删除 column：栏目关联删除
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    void delete(String id, String reason);

    /**
     * 删除栏目关联
     *
     * @param columnId 栏目id
     */
    void deleteColumnContact(String columnId);

    /**
     * 编辑普法资源(信息)
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    void edit(LeResourcesParam leResourcesParam);

    void audit(LeResourcesParam param);

    void merge(LeResourcesParam param);

    /**
     * 配置栏目
     */
    void contact(String id, String columnId, String columnName);

    /**
     * 取消关联栏目
     *
     * @param id 资源id
     */
    void escape(String id);

    /**
     * 撤回
     *
     * @param id 资源id
     */
    void withdraw(String id);

    /**
     * 查看普法资源(信息)
     *
     * <AUTHOR>
     * @date 2024-03-25 14:38:22
     */
    LeResources detail(LeResourcesParam leResourcesParam);


    void export(LeResourcesParam leResourcesParam);

    /**
     * 普法责任制编辑普法资源
     *
     * @param leResourcesParam
     */
    void editResource(LeResourcesParam leResourcesParam);

    /**
     * 普法资源点赞收藏转发浏览
     *
     * @param id
     * @param type
     */
    void viewById(String id, Integer type);

    /**
     * 推荐资源
     *
     * @return
     */
    List<LeColumn> recommend(String pid);

    /**
     * 获取用户点赞的资源id
     *
     * @param userId
     * @return
     */
    List<String> getLikeIds(String userId);

    /**
     * 获取用户收藏的资源id
     *
     * @param userId
     * @return
     */
    List<String> getStarIds(String userId);

    /**
     * 移动端资源分类查询
     *
     * @param leResourcesParam
     * @return
     */
    PageResult<LeResources> mobilePage(LeResourcesParam leResourcesParam);

    /**
     * 市级资源统计
     *
     * @param name
     * @return
     */
    List<HomeResourceVo> cityResource(String name);

    /**
     * 省级资源统计
     *
     * @return
     */
    List<HomeResourceVo> provinceResource();

    /**
     * 市级信息统计
     *
     * @param name
     * @return
     */
    List<HomeResourceVo> cityInformation(String name);

    /**
     * 省级信息统计
     *
     * @return
     */
    List<HomeResourceVo> provinceInformation();

    /**
     * 普法资源库分页
     *
     * @param leResourcesParam
     * @return
     */
    PageResult<LeResources> storePage(LeResourcesParam leResourcesParam);

    /**
     * 导出图片压缩包
     * @param leResourcesParam
     */
    void exportPic(LeResourcesParam leResourcesParam);


    /**
     * 导出word
     * @param leResourcesParam
     */
    void exportWord(LeResourcesParam leResourcesParam);

    /**
     * 更新资源栏目
     */
    void updateResourceColumn();

    /**
     * 随机刷阅读量
     */
    void randomView();

    /**
     * 处理旧数据，根据标题和内容匹配字典值更新主题分类和面向人群分类
     */
    void processOldDataForClassification();

    /**
     * 通过旧标签数据处理分类信息
     * 从law_education_resources_label_old和law_education_resources_label_contact_old表中获取标签信息
     * 然后根据标签名称匹配字典来更新资源的人群分类和主题分类
     */
    void processOldDataForClassificationByLabel();

    /**
     * 通过新标签规则处理标签关联
     * 包含两种处理方式：1.按原标签对应 2.按标题关键字对应
     * 往law_education_resources_label_contact表中添加数据
     */
    void processDataByNewLabelRules();

    /**
     * 批量获取指定时间段内的LeResources附件并下载到本地
     *
     * @param beginTime 开始时间，格式为yyyy-MM-dd
     * @param endTime 结束时间，格式为yyyy-MM-dd
     * @param localPath 本地保存路径
     * @param fileTypes 文件类型过滤，如jpg,png,mp4等，逗号分隔，为空则下载所有类型
     * @return 下载成功的文件数量
     * @throws IOException 下载过程中可能出现的IO异常
     */
    int batchDownloadAttachments(String beginTime, String endTime, String localPath, String fileTypes) throws IOException;

    /**
     * 普法信息报送统计-分页
     */
    PageResult<JSONObject> lawReportPage(String areaName);

    List<String> selectHasLabel();

    /**
     * 批量导入
     *
     * @param excelPath
     * @param fileFolderPath
     * @return
     */
    Object importExcelAndPath(String excelPath, String fileFolderPath);
}
