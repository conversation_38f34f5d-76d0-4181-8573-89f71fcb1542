package com.concise.modular.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * 文件上传工具类
 */
public class FileUploaderUtils {

    public static String upload(String originUrl, String targetUrl) {
//        String ossUrl = "https://shidu-legalrobot-bz.oss-cn-hangzhou.aliyuncs.com/plsms/lpal/video/20230703/cover/40f70313d10b4cd68ed02286ac66c8fe/1688367424611_5de73888_cover.jpg";
//        String uploadUrl = "https://zlpf.zjsft.gov.cn/api/sysFileInfo/upload";

        try {
            // 从OSS链接获取文件流
            InputStream fileStream = new URL(originUrl).openStream();
            // 获取文件名
            String fileName = originUrl.substring(originUrl.lastIndexOf('/') + 1);

            // 创建临时文件
            File tempFile = FileUtil.writeFromStream(fileStream, FileUtil.getTmpDirPath() + "/" + fileName);

            // 创建MultipartFile对象
            MultipartFile multipartFile = createMultipartFile(tempFile);

            // 获取文件大小
            long fileSize = multipartFile.getSize();

            // 定义500MB的字节数
            long maxSize = 500 * 1024 * 1024; // 500MB

//            // 判断文件大小是否大于500MB
//            if (fileSize > maxSize) {
//                System.out.println("文件大小超过了500MB");
//                return "500";
//            } else {
//                System.out.println("文件大小是：" + (fileSize / 1024 / 1024) + "mb");
//            }
            System.out.println("文件大小是：" + (fileSize / 1024 / 1024) + "mb");

            // 发送POST请求上传文件
            ResponseEntity<String> response = uploadFile(targetUrl, multipartFile);

            // 处理响应结果
            if (response.getStatusCode() == HttpStatus.OK) {
                System.out.println("文件上传成功!");
                System.out.println("响应结果: " + response.getBody());
            } else {
                System.out.println("文件上传失败!");
                System.out.println("响应码: " + response.getStatusCodeValue());
                System.out.println("响应结果: " + response.getBody());
            }
            return response.getBody();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static MultipartFile createMultipartFile(File file) throws IOException {
        byte[] bytes = FileUtil.readBytes(file);
        InputStream inputStream = new ByteArrayInputStream(bytes);
        return new MultipartFileImpl(inputStream, file.getName(), "application/octet-stream", bytes.length);
    }

    public static ResponseEntity<String> uploadFile(String url, MultipartFile file) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", file.getResource());

        return restTemplate.postForEntity(url, new HttpEntity<>(body, headers), String.class);
    }

    private static class MultipartFileImpl implements MultipartFile {

        private final InputStream inputStream;
        private final String originalFilename;
        private final String contentType;
        private final long size;

        MultipartFileImpl(InputStream inputStream, String originalFilename, String contentType, long size) {
            this.inputStream = inputStream;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.size = size;
        }

        @Override
        public String getName() {
            return null;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return size == 0;
        }

        @Override
        public long getSize() {
            return size;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return IoUtil.readBytes(inputStream);
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return inputStream;
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            IoUtil.copy(inputStream, FileUtil.getOutputStream(dest));
        }
    }
}
